# Stage 1 Implementation Plan - <PERSON> Rides Flutter App

## Overview

Stage 1 focuses on **Authentication & User Management** - establishing the foundation for user registration, login, profile management, and basic navigation structure. This stage implements the core user identity system that all other features will build upon.

## 📋 Task List by Features/Categories

### 🔐 **Authentication System**

- [ ] **AUTH-001**: Set up Flutter project structure with clean architecture
- [ ] **AUTH-002**: Implement authentication service layer
- [ ] **AUTH-003**: Create login screen with email/password validation
- [ ] **AUTH-004**: Create registration screen with user type selection (rider/driver)
- [ ] **AUTH-005**: Implement logout functionality
- [ ] **AUTH-006**: Add remember me functionality with secure token storage
- [ ] **AUTH-007**: Create password recovery flow (if backend supports it)
- [ ] **AUTH-008**: Implement token verification and refresh logic

### 👤 **User Profile Management**

- [ ] **PROFILE-001**: Create user profile models (User, Rider, Driver)
- [ ] **PROFILE-002**: Build profile creation/edit screens
- [ ] **PROFILE-003**: Implement profile image upload functionality
- [ ] **PROFILE-004**: Create rider-specific profile setup
- [ ] **PROFILE-005**: Create driver-specific profile setup with vehicle info
- [ ] **PROFILE-006**: Add emergency contact management for riders
- [ ] **PROFILE-007**: Implement profile completeness validation
- [ ] **PROFILE-008**: Create profile viewing screen

### 🏗️ **Core App Structure**

- [ ] **CORE-001**: Set up dependency injection (GetIt)
- [ ] **CORE-002**: Implement state management with Riverpod
- [ ] **CORE-003**: Create base repository pattern for API calls
- [ ] **CORE-004**: Set up HTTP client with interceptors for auth
- [ ] **CORE-005**: Implement error handling middleware
- [ ] **CORE-006**: Create app routing structure with AutoRoute
- [ ] **CORE-007**: Set up app theme based on design system
- [ ] **CORE-008**: Implement secure storage for tokens

### 🎨 **UI Components Library**

- [ ] **UI-001**: Create design system components (buttons, inputs, cards)
- [ ] **UI-002**: Implement custom app bar component
- [ ] **UI-003**: Create loading states and skeleton screens
- [ ] **UI-004**: Build form validation components
- [ ] **UI-005**: Create custom navigation components
- [ ] **UI-006**: Implement image picker/display components
- [ ] **UI-007**: Create error display components
- [ ] **UI-008**: Build bottom sheet components

### 🔄 **Navigation & Flow**

- [ ] **NAV-001**: Create splash screen with app initialization
- [ ] **NAV-002**: Implement onboarding flow for new users
- [ ] **NAV-003**: Set up main app navigation structure
- [ ] **NAV-004**: Create role-based navigation (rider vs driver)
- [ ] **NAV-005**: Implement deep linking support
- [ ] **NAV-006**: Add navigation guards for authentication
- [ ] **NAV-007**: Create user type selection flow

## 🔧 **Technical Implementation Details**

### **Project Structure**

```
lib/
├── main.dart
├── app/
│   ├── app.dart
│   ├── routes/
│   ├── theme/
│   └── constants/
├── core/
│   ├── api/
│   ├── models/
│   ├── repositories/
│   ├── services/
│   ├── storage/
│   └── utils/
├── features/
│   ├── authentication/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── repositories/
│   │   ├── screens/
│   │   └── widgets/
│   ├── profile/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── repositories/
│   │   ├── screens/
│   │   └── widgets/
│   └── shared/
│       ├── widgets/
│       └── utils/
└── gen/
    ├── assets.gen.dart
    └── colors.gen.dart
```

### **Required Dependencies**

```yaml
dependencies:
  # State Management
  flutter_riverpod: ^2.4.0
  
  # Navigation
  auto_route: ^7.8.4
  
  # Dependency Injection
  get_it: ^7.6.4
  
  # HTTP & API
  dio: ^5.3.2
  retrofit: ^4.0.3
  
  # Local Storage
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2
  
  # Models & Serialization
  freezed: ^2.4.6
  json_annotation: ^4.8.1
  
  # UI & Theming
  flutter_screenutil: ^5.9.0
  cached_network_image: ^3.3.0
  
  # Image Handling
  image_picker: ^1.0.4
  
  # Validation
  formz: ^0.6.1
  
  # Utils
  intl: ^0.18.1
  logger: ^2.0.2
  
  # Google Fonts
  google_fonts: ^6.1.0

dev_dependencies:
  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.6
  auto_route_generator: ^7.3.2
  
  # Assets Generation
  flutter_gen_runner: ^5.3.2
```

## 📱 **API Endpoints to Implement**

### **Authentication Endpoints**

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `GET /api/v1/auth/profile` - Get user profile
- `PUT /api/v1/auth/profile` - Update user profile
- `GET /api/v1/auth/profile/complete` - Get complete profile with type-specific data
- `POST /api/v1/auth/profile/image` - Upload profile image
- `DELETE /api/v1/auth/profile/image` - Delete profile image
- `GET /api/v1/auth/verify-token` - Verify JWT token

### **Rider Profile Endpoints**

- `GET /api/v1/riders/profile` - Get rider profile
- `PUT /api/v1/riders/profile` - Update rider profile
- `POST /api/v1/riders/profile` - Create rider profile
- `GET /api/v1/riders/emergency-contact` - Get emergency contact
- `POST /api/v1/riders/emergency-contact` - Add emergency contact

### **Driver Profile Endpoints**

- `GET /api/v1/drivers/profile` - Get driver profile
- `PUT /api/v1/drivers/profile` - Update driver profile
- `POST /api/v1/drivers/profile` - Create driver profile
- `GET /api/v1/drivers/vehicle` - Get vehicle info
- `POST /api/v1/drivers/vehicle` - Add vehicle info

## 🎨 **Design System Implementation**

### **Color Palette (from design.json)**

```dart
class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF00B5A5);  // Teal
  static const Color primaryDark = Color(0xFF008B7A);
  static const Color primaryLight = Color(0xFF4DD0C7);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color mediumGray = Color(0xFFE0E0E0);
  static const Color darkGray = Color(0xFF666666);
  static const Color charcoal = Color(0xFF2C2C2C);
  static const Color black = Color(0xFF000000);
  
  // Accent Colors
  static const Color blue = Color(0xFF007AFF);
  static const Color yellow = Color(0xFFFFD700);
  static const Color green = Color(0xFF34C759);
  static const Color orange = Color(0xFFFF9500);
  
  // Status Colors
  static const Color success = Color(0xFF34C759);
  static const Color warning = Color(0xFFFF9500);
  static const Color error = Color(0xFFFF3B30);
  static const Color info = Color(0xFF007AFF);
}
```

### **Typography**

```dart
class AppTextStyles {
  // Font families (from design.json)
  static const String primaryFontFamily = 'Roboto';
  static const String secondaryFontFamily = 'Inter';
  static const String fallbackFontFamily = 'Roboto, Inter, Open Sans, sans-serif';
  
  // Google Fonts configuration
  static const List<String> googleFontsImports = [
    'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap',
  ];
  
  // Text styles using primary font (Roboto)
  static const TextStyle heading1 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w700,
    height: 1.2,
  );
  
  static const TextStyle heading2 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.2,
  );
  
  static const TextStyle body1 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    height: 1.4,
  );
  
  static const TextStyle body2 = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    height: 1.4,
  );
  
  static const TextStyle caption = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.4,
  );
  
  // Secondary text styles using Inter font for special cases
  static const TextStyle headingSecondary = TextStyle(
    fontFamily: secondaryFontFamily,
    fontSize: 24,
    fontWeight: FontWeight.w600,
    height: 1.2,
  );
  
  static const TextStyle bodySecondary = TextStyle(
    fontFamily: secondaryFontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    height: 1.4,
  );
}
```

## 📖 **Important Notes & Guidelines**

### **Code Standards**

1. **Clean Architecture**: Follow clean architecture principles with clear separation of concerns
2. **Dart Guidelines**: Use PascalCase for classes, camelCase for variables/functions
3. **File Naming**: Use snake_case for files and directories
4. **Comments**: Comment extensively as requested by user
5. **Type Safety**: Always declare types, avoid dynamic/any types
6. **Error Handling**: Implement comprehensive error handling with user-friendly messages

### **State Management**

- Use **Riverpod** for state management
- Controllers handle business logic and update UI state
- Keep controllers lightweight with single responsibilities
- Use `freezed` for immutable state classes

### **API Integration**

- Base URL: `http://localhost:8000/api/v1`
- Use **Dio** with **Retrofit** for type-safe API calls
- Implement JWT token management with automatic refresh
- Add request/response interceptors for logging and auth
- Handle network errors gracefully

### **Security**

- Store JWT tokens in **Flutter Secure Storage**
- Validate all user inputs on client-side
- Implement proper session management
- Use HTTPS in production environment

### **UI/UX Guidelines**

- Follow the provided design system strictly
- Implement responsive design using **ScreenUtil**
- Use skeleton loading screens for better UX
- Maintain 44px minimum touch targets for accessibility
- Support dynamic text scaling
- Use **Google Fonts** package for Roboto, Inter, and Open Sans fonts
- Implement font fallback hierarchy as specified in design system

## ✅ **Stage 1 Completion Criteria**

### **Functional Requirements**

- [ ] **User Registration**: Users can register as rider or driver with email/password
- [ ] **User Login**: Existing users can log in with email/password
- [ ] **Profile Setup**: Users can complete their basic profile information
- [ ] **Profile Image**: Users can upload and manage profile images
- [ ] **User Type Specific Setup**:
  - Riders can add emergency contact information
  - Drivers can add vehicle information and license details
- [ ] **Profile Management**: Users can view and edit their profile information
- [ ] **Session Management**: App properly handles login/logout states
- [ ] **Navigation**: Smooth navigation between screens based on user state

### **Technical Requirements**

- [ ] **API Integration**: All Stage 1 API endpoints are properly integrated
- [ ] **Error Handling**: Comprehensive error handling with user feedback
- [ ] **State Management**: Proper state management implementation
- [ ] **Local Storage**: Secure token storage and session persistence
- [ ] **Form Validation**: Client-side validation for all forms
- [ ] **Loading States**: Proper loading indicators throughout the app
- [ ] **Responsive Design**: App works on various screen sizes
- [ ] **Performance**: Smooth animations and transitions

### **Quality Assurance**

- [ ] **Code Quality**: Clean, well-commented, and maintainable code
- [ ] **Type Safety**: No usage of dynamic types, proper type declarations
- [ ] **Design Consistency**: Adheres to the provided design system
- [ ] **Accessibility**: Meets basic accessibility requirements
- [ ] **Error Scenarios**: App handles network failures and edge cases gracefully
- [ ] **Security**: Proper handling of sensitive data and authentication tokens

### **User Experience**

- [ ] **Onboarding Flow**: Smooth first-time user experience
- [ ] **Intuitive Navigation**: Users can easily navigate the app
- [ ] **Visual Feedback**: Clear feedback for user actions
- [ ] **Error Messages**: Helpful and actionable error messages
- [ ] **Performance**: App feels responsive and fast

## 🔍 **Resources to keep in mind** ( Not necessarily must-have)

### **Flutter & Dart**

#### **Flutter Clean Architecture patterns**

- **Official Guide**: [Flutter Architecture Samples](https://github.com/brianegan/flutter_architecture_samples)
- **Key Finding**: Use feature-based folder structure with `presentation/domain/data` layers
- **Best Practice**: Separate business logic from UI using repositories and use cases
- **Implementation**: Create `core/` folder for shared utilities and `features/` for each major functionality

#### **Form validation with Formz**

- **Official Package**: [formz](https://pub.dev/packages/formz)
- **Key Finding**: Provides reusable form input validation with built-in error handling
- **Best Practice**: Create custom input classes extending `FormzInput<T, E>` for each field type
- **Implementation**: Use with Riverpod for reactive form validation and state management

### **API Integration**

#### **Dio interceptors for authentication**

- **Official Package**: [dio](https://pub.dev/packages/dio)
- **Key Finding**: Use `InterceptorsWrapper` for automatic token injection and refresh
- **Best Practice**: Implement retry logic for 401 errors with token refresh
- **Implementation**: Create `AuthInterceptor` class to handle JWT token lifecycle

```dart
dio.interceptors.add(InterceptorsWrapper(
  onRequest: (options, handler) {
    options.headers['Authorization'] = 'Bearer $token';
    handler.next(options);
  },
));
```

#### **JWT token management in Flutter**

- **Flutter Secure Storage**: [flutter_secure_storage](https://pub.dev/packages/flutter_secure_storage)
- **Key Finding**: Store tokens securely using device keychain/keystore
- **Best Practice**: Implement automatic token refresh 5 minutes before expiry
- **Implementation**: Create `TokenManager` service with refresh logic and error handling

#### **File upload implementation with multipart**

- **Dio Multipart**: Built into Dio package
- **Key Finding**: Use `MultipartFile.fromFile()` for file uploads with progress tracking
- **Best Practice**: Implement upload progress indicators and error retry mechanisms
- **Implementation**: Create dedicated `FileUploadService` with progress callbacks

### **UI/UX**

#### **Typography & Font Implementation**

- **Flutter Font Guide**: [Use a custom font - Flutter](https://docs.flutter.dev/cookbook/design/fonts)
- **Google Fonts Package**: [google_fonts](https://pub.dev/packages/google_fonts)
- **Key Finding**: Use Google Fonts package for easy font integration without manual asset management
- **Best Practice**: Implement font fallback hierarchy and cache fonts for offline use
- **Typography Guide**: [Beginners Guide To Text Styling in Flutter](https://medium.com/flutter-community/beginners-guide-to-text-styling-in-flutter-3939085d6607)

```dart
// Using Google Fonts with fallback
style: GoogleFonts.roboto(
  fontSize: 16,
  fontWeight: FontWeight.w400,
  fallbackFonts: ['Inter', 'Open Sans'],
)
```

#### **Material Design 3 guidelines**

- **Official M3 Guide**: [Material Design 3](https://m3.material.io/)
- **Flutter M3 Support**: [Migrate to Material 3](https://docs.flutter.dev/ui/design/material)
- **Key Finding**: Use `useMaterial3: true` in ThemeData for new design tokens
- **Best Practice**: Implement dynamic color schemes and proper component theming
- **Implementation**: Use `ColorScheme.fromSeed()` for consistent color palettes

#### **Flutter responsive design patterns**

- **Adaptive Design Guide**: [Building adaptive apps](https://docs.flutter.dev/ui/adaptive-responsive)
- **Key Finding**: Use `LayoutBuilder` and `MediaQuery` for responsive layouts
- **Best Practice**: Implement breakpoint-based layouts and adaptive navigation
- **Implementation**: Create responsive utility classes for consistent spacing and sizing

#### **Custom theme implementation**

- **Theme Guide**: [Use themes to share colors and font styles](https://docs.flutter.dev/cookbook/design/themes)
- **Key Finding**: Extend `ThemeExtension` for custom design system properties
- **Best Practice**: Create theme extensions for custom colors, spacing, and component styles
- **Implementation**: Use `Theme.of(context).extension<CustomTheme>()` for custom properties

### **Testing**

#### **Flutter widget testing**

- **Official Testing Guide**: [Flutter Testing](https://docs.flutter.dev/testing)
- **Key Finding**: Use `testWidgets()` for UI testing and `WidgetTester` for interactions
- **Best Practice**: Test widget behavior, not implementation details
- **Implementation**: Create test helpers for common widget setups and mock dependencies

#### **Unit testing with mockito**

- **Mockito Package**: [mockito](https://pub.dev/packages/mockito)
- **Key Finding**: Use `@GenerateMocks()` annotation for automatic mock generation
- **Best Practice**: Mock external dependencies (APIs, storage) for isolated testing
- **Implementation**: Create mock classes for repositories and services

#### **Integration testing setup**

- **Integration Testing**: [Flutter Integration Tests](https://docs.flutter.dev/testing/integration-tests)
- **Key Finding**: Use `integration_test` package for full app testing
- **Best Practice**: Test critical user flows (login, registration, profile setup)
- **Implementation**: Create separate test suites for different user journeys

### **Development Tools**

#### **Flutter dev tools profiling**

- **DevTools Guide**: [Flutter DevTools](https://docs.flutter.dev/tools/devtools)
- **Key Finding**: Use performance overlay and timeline to identify bottlenecks
- **Best Practice**: Profile on physical devices, especially lower-end ones
- **Implementation**: Enable performance monitoring in debug builds

#### **Code generation with build_runner**

- **Build Runner Package**: [build_runner](https://pub.dev/packages/build_runner)
- **Key Finding**: Use `dart run build_runner watch` for continuous code generation
- **Best Practice**: Configure generation in `build.yaml` for custom rules
- **Implementation**: Run generation after any changes to annotated classes

#### **Asset generation with flutter_gen**

- **Flutter Gen Package**: [flutter_gen](https://pub.dev/packages/flutter_gen)
- **Key Finding**: Generates type-safe asset classes for images, fonts, and colors
- **Best Practice**: Use generated asset classes instead of string literals
- **Implementation**: Configure in `pubspec.yaml` under `flutter_gen` section

#### **Dynamic UI Patterns**

- **JSON-Based UI**: [Dynamic UI Generation in Flutter](https://medium.com/@sd2b/dynamic-ui-generation-in-flutter-using-json-based-configurations-8eb4460d7f1d)
- **Key Finding**: Create widget builders that parse JSON configurations for flexible UIs
- **Best Practice**: Use factory patterns for widget generation from data
- **Implementation**: Consider for admin-configurable UI elements and A/B testing

## 🚀 **Next Steps After Stage 1**

Once Stage 1 is complete, the foundation will be ready for:

- **Stage 2**: Ride booking system and fixed pricing
- **Stage 3**: Real-time location tracking and mapping
- **Stage 4**: Driver-rider messaging system
- **Stage 5**: Payment integration with Stripe
- **Stage 6**: Rating system and trip history

This plan ensures a solid foundation for the Lucian Rides app with proper authentication, user management, and a scalable architecture for future features.
