import 'dart:async';

import 'package:logger/logger.dart';

import 'storage_service.dart';
import 'token_service.dart';

/// Session manager for handling user sessions and automatic logout
class SessionManager {
  SessionManager({
    required StorageService storageService,
    required TokenService tokenService,
  })  : _storageService = storageService,
        _tokenService = tokenService;

  final StorageService _storageService;
  final TokenService _tokenService;
  final Logger _logger = Logger();

  Timer? _sessionTimer;
  Timer? _activityTimer;
  StreamController<SessionEvent>? _sessionEventController;

  /// Stream of session events
  Stream<SessionEvent> get sessionEvents {
    _sessionEventController ??= StreamController<SessionEvent>.broadcast();
    return _sessionEventController!.stream;
  }

  /// Initialize session management
  Future<void> initialize() async {
    _logger.d('Initializing session manager');
    
    // Check if user is authenticated
    final bool isAuthenticated = await _tokenService.isAuthenticated();
    if (isAuthenticated) {
      await startSession();
    }
  }

  /// Start user session
  Future<void> startSession() async {
    _logger.i('Starting user session');
    
    // Update last login time
    await _storageService.updateSessionActivity();
    
    // Start session monitoring
    _startSessionMonitoring();
    
    // Start token refresh scheduling
    _tokenService.scheduleTokenRefresh();
    
    // Emit session started event
    _emitSessionEvent(SessionEvent.sessionStarted);
  }

  /// End user session
  Future<void> endSession({bool isAutoLogout = false}) async {
    _logger.i('Ending user session (auto: $isAutoLogout)');
    
    // Stop timers
    _stopSessionMonitoring();
    _tokenService.cancelTokenRefresh();
    
    // Clear session data
    await _storageService.clearAll();
    
    // Emit session ended event
    _emitSessionEvent(
      isAutoLogout ? SessionEvent.sessionExpired : SessionEvent.sessionEnded,
    );
  }

  /// Update session activity (call on user interaction)
  Future<void> updateActivity() async {
    if (!_storageService.isAutoLogoutEnabled()) return;
    
    await _storageService.updateSessionActivity();
    _logger.d('Session activity updated');
    
    // Reset session timer
    _resetSessionTimer();
  }

  /// Check if session is valid
  Future<bool> isSessionValid() async {
    // Check if user is authenticated
    final bool isAuthenticated = await _tokenService.isAuthenticated();
    if (!isAuthenticated) return false;
    
    // Check if session has expired
    if (_storageService.isSessionExpired()) {
      _logger.w('Session has expired');
      await endSession(isAutoLogout: true);
      return false;
    }
    
    return true;
  }

  /// Start session monitoring
  void _startSessionMonitoring() {
    if (!_storageService.isAutoLogoutEnabled()) {
      _logger.d('Auto logout is disabled, skipping session monitoring');
      return;
    }
    
    _resetSessionTimer();
    
    // Check session validity every minute
    _activityTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      final bool isValid = await isSessionValid();
      if (!isValid) {
        timer.cancel();
      }
    });
  }

  /// Stop session monitoring
  void _stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
    
    _activityTimer?.cancel();
    _activityTimer = null;
  }

  /// Reset session timer
  void _resetSessionTimer() {
    _sessionTimer?.cancel();
    
    final int timeoutMinutes = _storageService.getSessionTimeout();
    _sessionTimer = Timer(Duration(minutes: timeoutMinutes), () async {
      _logger.w('Session timeout reached, logging out user');
      await endSession(isAutoLogout: true);
    });
  }

  /// Emit session event
  void _emitSessionEvent(SessionEvent event) {
    _sessionEventController?.add(event);
  }

  /// Dispose resources
  void dispose() {
    _stopSessionMonitoring();
    _tokenService.cancelTokenRefresh();
    _sessionEventController?.close();
    _sessionEventController = null;
  }
}

/// Session events
enum SessionEvent {
  sessionStarted,
  sessionEnded,
  sessionExpired,
  sessionWarning,
}

/// Session warning service for notifying users before logout
class SessionWarningService {
  SessionWarningService({
    required SessionManager sessionManager,
    required StorageService storageService,
  })  : _sessionManager = sessionManager,
        _storageService = storageService;

  final SessionManager _sessionManager;
  final StorageService _storageService;
  final Logger _logger = Logger();

  Timer? _warningTimer;
  StreamController<int>? _warningController;

  /// Stream of warning countdown (minutes remaining)
  Stream<int> get warningCountdown {
    _warningController ??= StreamController<int>.broadcast();
    return _warningController!.stream;
  }

  /// Start warning countdown
  void startWarningCountdown() {
    if (!_storageService.isAutoLogoutEnabled()) return;
    
    final int timeoutMinutes = _storageService.getSessionTimeout();
    final int warningMinutes = (timeoutMinutes * 0.8).round(); // Warn at 80% of timeout
    
    _warningTimer = Timer(Duration(minutes: warningMinutes), () {
      _startCountdown();
    });
  }

  /// Start the actual countdown
  void _startCountdown() {
    final int timeoutMinutes = _storageService.getSessionTimeout();
    final int warningMinutes = (timeoutMinutes * 0.2).round(); // Last 20% of timeout
    
    int remainingMinutes = warningMinutes;
    
    // Emit initial warning
    _warningController?.add(remainingMinutes);
    
    // Start countdown timer
    Timer.periodic(const Duration(minutes: 1), (timer) {
      remainingMinutes--;
      
      if (remainingMinutes <= 0) {
        timer.cancel();
        _warningController?.add(0);
      } else {
        _warningController?.add(remainingMinutes);
      }
    });
  }

  /// Extend session (user chose to stay logged in)
  Future<void> extendSession() async {
    _logger.i('User extended session');
    
    // Cancel warning timer
    _warningTimer?.cancel();
    _warningTimer = null;
    
    // Update session activity
    await _sessionManager.updateActivity();
    
    // Restart warning countdown
    startWarningCountdown();
  }

  /// Dispose resources
  void dispose() {
    _warningTimer?.cancel();
    _warningController?.close();
    _warningController = null;
  }
}

/// Biometric authentication service
class BiometricAuthService {
  BiometricAuthService({
    required StorageService storageService,
  }) : _storageService = storageService;

  final StorageService _storageService;
  final Logger _logger = Logger();

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    // TODO: Implement biometric availability check
    // This would use local_auth package
    return false;
  }

  /// Check if biometric authentication is enabled
  bool isBiometricEnabled() {
    return _storageService.isBiometricEnabled();
  }

  /// Enable biometric authentication
  Future<bool> enableBiometric() async {
    try {
      final bool isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        _logger.w('Biometric authentication not available');
        return false;
      }

      // TODO: Implement biometric setup
      // This would prompt user for biometric authentication
      
      await _storageService.setBiometricEnabled(true);
      _logger.i('Biometric authentication enabled');
      return true;
    } catch (e) {
      _logger.e('Error enabling biometric authentication: $e');
      return false;
    }
  }

  /// Disable biometric authentication
  Future<void> disableBiometric() async {
    await _storageService.setBiometricEnabled(false);
    _logger.i('Biometric authentication disabled');
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometric() async {
    try {
      if (!isBiometricEnabled()) {
        _logger.w('Biometric authentication not enabled');
        return false;
      }

      // TODO: Implement biometric authentication
      // This would use local_auth package to authenticate
      
      _logger.i('Biometric authentication successful');
      return true;
    } catch (e) {
      _logger.e('Biometric authentication failed: $e');
      return false;
    }
  }
}
