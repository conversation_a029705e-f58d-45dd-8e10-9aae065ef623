import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling secure and non-secure storage operations
/// Uses FlutterSecureStorage for sensitive data and SharedPreferences for general data
class StorageService {
  const StorageService({
    required FlutterSecureStorage secureStorage,
    required SharedPreferences sharedPreferences,
  }) : _secureStorage = secureStorage,
       _sharedPreferences = sharedPreferences;

  final FlutterSecureStorage _secureStorage;
  final SharedPreferences _sharedPreferences;

  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  static const String _rememberMeKey = 'remember_me';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _rememberedEmailKey = 'remembered_email';
  static const String _lastLoginTimeKey = 'last_login_time';
  static const String _sessionTimeoutKey = 'session_timeout';
  static const String _autoLogoutEnabledKey = 'auto_logout_enabled';
  static const String _biometricEnabledKey = 'biometric_enabled';

  /// Store access token securely
  Future<void> storeAccessToken(String token) async {
    await _secureStorage.write(key: _accessTokenKey, value: token);
  }

  /// Retrieve access token
  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: _accessTokenKey);
  }

  /// Store refresh token securely
  Future<void> storeRefreshToken(String token) async {
    await _secureStorage.write(key: _refreshTokenKey, value: token);
  }

  /// Retrieve refresh token
  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: _refreshTokenKey);
  }

  /// Store user data as JSON
  Future<void> storeUserData(Map<String, dynamic> userData) async {
    final String jsonString = jsonEncode(userData);
    await _secureStorage.write(key: _userDataKey, value: jsonString);
  }

  /// Retrieve user data
  Future<Map<String, dynamic>?> getUserData() async {
    final String? jsonString = await _secureStorage.read(key: _userDataKey);
    if (jsonString != null) {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    }
    return null;
  }

  /// Store remember me preference
  Future<void> setRememberMe(bool rememberMe) async {
    await _sharedPreferences.setBool(_rememberMeKey, rememberMe);
  }

  /// Get remember me preference
  bool getRememberMe() {
    return _sharedPreferences.getBool(_rememberMeKey) ?? false;
  }

  /// Set onboarding completed status
  Future<void> setOnboardingCompleted(bool completed) async {
    await _sharedPreferences.setBool(_onboardingCompletedKey, completed);
  }

  /// Check if onboarding is completed
  bool isOnboardingCompleted() {
    return _sharedPreferences.getBool(_onboardingCompletedKey) ?? false;
  }

  /// Store remembered email
  Future<void> setRememberedEmail(String? email) async {
    if (email != null) {
      await _sharedPreferences.setString(_rememberedEmailKey, email);
    } else {
      await _sharedPreferences.remove(_rememberedEmailKey);
    }
  }

  /// Get remembered email
  String? getRememberedEmail() {
    return _sharedPreferences.getString(_rememberedEmailKey);
  }

  /// Set last login time
  Future<void> setLastLoginTime(DateTime time) async {
    await _sharedPreferences.setInt(
      _lastLoginTimeKey,
      time.millisecondsSinceEpoch,
    );
  }

  /// Get last login time
  DateTime? getLastLoginTime() {
    final int? timestamp = _sharedPreferences.getInt(_lastLoginTimeKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  /// Set session timeout (in minutes)
  Future<void> setSessionTimeout(int minutes) async {
    await _sharedPreferences.setInt(_sessionTimeoutKey, minutes);
  }

  /// Get session timeout (in minutes)
  int getSessionTimeout() {
    return _sharedPreferences.getInt(_sessionTimeoutKey) ??
        30; // Default 30 minutes
  }

  /// Set auto logout enabled
  Future<void> setAutoLogoutEnabled(bool enabled) async {
    await _sharedPreferences.setBool(_autoLogoutEnabledKey, enabled);
  }

  /// Check if auto logout is enabled
  bool isAutoLogoutEnabled() {
    return _sharedPreferences.getBool(_autoLogoutEnabledKey) ??
        true; // Default enabled
  }

  /// Set biometric authentication enabled
  Future<void> setBiometricEnabled(bool enabled) async {
    await _sharedPreferences.setBool(_biometricEnabledKey, enabled);
  }

  /// Check if biometric authentication is enabled
  bool isBiometricEnabled() {
    return _sharedPreferences.getBool(_biometricEnabledKey) ?? false;
  }

  /// Check if session has expired
  bool isSessionExpired() {
    if (!isAutoLogoutEnabled()) return false;

    final DateTime? lastLogin = getLastLoginTime();
    if (lastLogin == null) return true;

    final int timeoutMinutes = getSessionTimeout();
    final DateTime expiryTime = lastLogin.add(
      Duration(minutes: timeoutMinutes),
    );

    return DateTime.now().isAfter(expiryTime);
  }

  /// Update session activity
  Future<void> updateSessionActivity() async {
    await setLastLoginTime(DateTime.now());
  }

  /// Clear all stored data (logout)
  Future<void> clearAll() async {
    await _secureStorage.deleteAll();
    await _sharedPreferences.clear();
  }

  /// Clear only authentication data
  Future<void> clearAuthData() async {
    await _secureStorage.delete(key: _accessTokenKey);
    await _secureStorage.delete(key: _refreshTokenKey);
    await _secureStorage.delete(key: _userDataKey);
  }

  /// Check if user is logged in (has access token)
  Future<bool> isLoggedIn() async {
    final String? token = await getAccessToken();
    return token != null && token.isNotEmpty;
  }

  /// Store a generic string value securely
  Future<void> storeSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  /// Retrieve a generic string value securely
  Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  /// Store a generic value in shared preferences
  Future<void> storeValue<T>(String key, T value) async {
    if (value is String) {
      await _sharedPreferences.setString(key, value);
    } else if (value is int) {
      await _sharedPreferences.setInt(key, value);
    } else if (value is double) {
      await _sharedPreferences.setDouble(key, value);
    } else if (value is bool) {
      await _sharedPreferences.setBool(key, value);
    } else if (value is List<String>) {
      await _sharedPreferences.setStringList(key, value);
    } else {
      throw ArgumentError(
        'Unsupported type for shared preferences: ${T.toString()}',
      );
    }
  }

  /// Retrieve a generic value from shared preferences
  T? getValue<T>(String key) {
    if (T == String) {
      return _sharedPreferences.getString(key) as T?;
    } else if (T == int) {
      return _sharedPreferences.getInt(key) as T?;
    } else if (T == double) {
      return _sharedPreferences.getDouble(key) as T?;
    } else if (T == bool) {
      return _sharedPreferences.getBool(key) as T?;
    } else if (T == List<String>) {
      return _sharedPreferences.getStringList(key) as T?;
    } else {
      throw ArgumentError(
        'Unsupported type for shared preferences: ${T.toString()}',
      );
    }
  }
}
