import 'package:logger/logger.dart';

import '../exceptions/api_exception.dart';
import '../models/api_response.dart';
import '../repositories/auth_repository.dart';
import 'token_service.dart';

/// Service for handling authentication business logic
class AuthService {
  AuthService({
    required AuthRepository authRepository,
    required TokenService tokenService,
  }) : _authRepository = authRepository,
       _tokenService = tokenService;

  final AuthRepository _authRepository;
  final TokenService _tokenService;
  final Logger _logger = Logger();

  /// Register a new user
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
  }) async {
    _logger.i('Registering user: $email');

    try {
      final Map<String, dynamic> response = await _authRepository.register(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        userType: userType,
        phoneNumber: phoneNumber,
      );

      await _handleAuthenticationSuccess(response);
      _logger.i('User registered successfully: $email');
      return ApiResponse.success(response);
    } catch (e) {
      if (e is ApiException) {
        _logger.e('Registration failed: ${e.message}');
        return ApiResponse.error(e);
      } else {
        _logger.e('Registration failed: $e');
        return ApiResponse.error(
          ApiException(message: 'Registration failed', originalError: e),
        );
      }
    }
  }

  /// Login user
  Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _logger.i('Logging in user: $email');

    try {
      final Map<String, dynamic> response = await _authRepository.login(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      await _handleAuthenticationSuccess(response);
      _logger.i('User logged in successfully: $email');
      return ApiResponse.success(response);
    } catch (e) {
      if (e is ApiException) {
        _logger.e('Login failed: ${e.message}');
        return ApiResponse.error(e);
      } else {
        _logger.e('Login failed: $e');
        return ApiResponse.error(
          ApiException(message: 'Login failed', originalError: e),
        );
      }
    }
  }

  /// Logout user
  Future<ApiResponse<Map<String, dynamic>>> logout() async {
    _logger.i('Logging out user');

    try {
      final Map<String, dynamic> response = await _authRepository.logout();

      // Always clear tokens, even if API call fails
      await _tokenService.clearTokens();

      _logger.i('User logged out');
      return ApiResponse.success(response);
    } catch (e) {
      // Always clear tokens, even if API call fails
      await _tokenService.clearTokens();

      if (e is ApiException) {
        return ApiResponse.error(e);
      } else {
        return ApiResponse.error(
          ApiException(message: 'Logout failed', originalError: e),
        );
      }
    }
  }

  /// Get current user profile
  Future<ApiResponse<Map<String, dynamic>>> getProfile() async {
    _logger.d('Getting user profile');

    try {
      final Map<String, dynamic> response = await _authRepository.getProfile();
      return ApiResponse.success(response);
    } catch (e) {
      if (e is ApiException) {
        return ApiResponse.error(e);
      } else {
        return ApiResponse.error(
          ApiException(message: 'Failed to get profile', originalError: e),
        );
      }
    }
  }

  /// Update user profile
  Future<ApiResponse<Map<String, dynamic>>> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
  }) async {
    _logger.i('Updating user profile');

    return await _authRepository.updateProfile(
      firstName: firstName,
      lastName: lastName,
      phoneNumber: phoneNumber,
      dateOfBirth: dateOfBirth,
      address: address,
      city: city,
      country: country,
    );
  }

  /// Get complete user profile
  Future<ApiResponse<Map<String, dynamic>>> getCompleteProfile() async {
    _logger.d('Getting complete user profile');

    return await _authRepository.getCompleteProfile();
  }

  /// Upload profile image
  Future<ApiResponse<Map<String, dynamic>>> uploadProfileImage({
    required String filePath,
  }) async {
    _logger.i('Uploading profile image');

    return await _authRepository.uploadProfileImage(filePath: filePath);
  }

  /// Delete profile image
  Future<ApiResponse<Map<String, dynamic>>> deleteProfileImage() async {
    _logger.i('Deleting profile image');

    return await _authRepository.deleteProfileImage();
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await _tokenService.isAuthenticated();
  }

  /// Verify current token
  Future<ApiResponse<Map<String, dynamic>>> verifyToken() async {
    _logger.d('Verifying token');

    // First check if we have a token locally
    final bool hasValidToken = await _tokenService.hasValidAccessToken();
    if (!hasValidToken) {
      return ApiResponse.error(
        const AuthenticationException(
          message: 'No valid token available',
          context: 'verifyToken',
        ),
      );
    }

    // Verify with server
    return await _authRepository.verifyToken();
  }

  /// Get user type
  Future<ApiResponse<Map<String, dynamic>>> getUserType() async {
    _logger.d('Getting user type');

    return await _authRepository.getUserType();
  }

  /// Get user permissions
  Future<ApiResponse<Map<String, dynamic>>> getUserPermissions() async {
    _logger.d('Getting user permissions');

    return await _authRepository.getUserPermissions();
  }

  /// Get profile completeness
  Future<ApiResponse<Map<String, dynamic>>> getProfileCompleteness() async {
    _logger.d('Getting profile completeness');

    return await _authRepository.getProfileCompleteness();
  }

  /// Get profile validation info
  Future<ApiResponse<Map<String, dynamic>>> getProfileValidation() async {
    _logger.d('Getting profile validation');

    return await _authRepository.getProfileValidation();
  }

  /// Deactivate account
  Future<ApiResponse<Map<String, dynamic>>> deactivateAccount() async {
    _logger.i('Deactivating account');

    return await _authRepository.deactivateAccount();
  }

  /// Handle successful authentication response
  Future<void> _handleAuthenticationSuccess(
    Map<String, dynamic> responseData,
  ) async {
    try {
      // Extract tokens from response
      final String? accessToken = responseData['access_token'] as String?;
      final String? refreshToken = responseData['refresh_token'] as String?;

      if (accessToken != null) {
        // Store tokens
        await _tokenService.storeTokens(
          accessToken: accessToken,
          refreshToken: refreshToken,
        );

        _logger.d('Tokens stored successfully');
      } else {
        _logger.w('No access token in authentication response');
      }
    } catch (e) {
      _logger.e('Error handling authentication success: $e');
      rethrow;
    }
  }

  /// Get current user ID from token
  Future<String?> getCurrentUserId() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.getUserIdFromToken(token);
    }
    return null;
  }

  /// Get current user email from token
  Future<String?> getCurrentUserEmail() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.getUserEmailFromToken(token);
    }
    return null;
  }

  /// Check if token needs refresh
  Future<bool> shouldRefreshToken() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.shouldRefreshToken(token);
    }
    return false;
  }

  /// Health check
  Future<ApiResponse<Map<String, dynamic>>> healthCheck() async {
    _logger.d('Performing health check');

    return await _authRepository.healthCheck();
  }
}
