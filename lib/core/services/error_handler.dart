import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../exceptions/app_exception.dart';

/// Global error handler service
class ErrorHandler {
  ErrorHandler() {
    _setupGlobalErrorHandling();
  }

  final Logger _logger = Logger();
  final StreamController<AppException> _errorController =
      StreamController<AppException>.broadcast();

  /// Stream of global errors
  Stream<AppException> get errorStream => _errorController.stream;

  /// Setup global error handling
  void _setupGlobalErrorHandling() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _logger.e(
        'Flutter Error: ${details.exception}',
        error: details.exception,
        stackTrace: details.stack,
      );

      final AppException exception = AppException.unknown(
        message: details.exception.toString(),
        userMessage: 'An unexpected error occurred',
        originalError: details.exception,
        stackTrace: details.stack,
      );

      _errorController.add(exception);
    };

    // Handle platform dispatcher errors (async errors)
    PlatformDispatcher.instance.onError = (error, stack) {
      _logger.e('Platform Error: $error', error: error, stackTrace: stack);

      final AppException exception = AppException.unknown(
        message: error.toString(),
        userMessage: 'An unexpected error occurred',
        originalError: error,
        stackTrace: stack,
      );

      _errorController.add(exception);
      return true;
    };
  }

  /// Handle and convert various error types to AppException
  AppException handleError(Object error, [StackTrace? stackTrace]) {
    _logger.e('Handling error: $error', error: error, stackTrace: stackTrace);

    AppException appException;

    if (error is AppException) {
      appException = error;
    } else if (error is DioException) {
      appException = _handleDioError(error);
    } else if (error is SocketException) {
      appException = _handleSocketError(error, stackTrace);
    } else if (error is TimeoutException) {
      appException = _handleTimeoutError(error, stackTrace);
    } else if (error is FormatException) {
      appException = _handleFormatError(error, stackTrace);
    } else {
      appException = _handleUnknownError(error, stackTrace);
    }

    // Emit error to global stream
    _errorController.add(appException);

    return appException;
  }

  /// Handle Dio errors
  AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return AppException.timeout(
          message: 'Request timeout: ${error.message}',
          code: 'TIMEOUT',
          userMessage: 'Request timed out. Please try again.',
          originalError: error,
          stackTrace: error.stackTrace,
        );

      case DioExceptionType.badResponse:
        return _handleHttpError(error);

      case DioExceptionType.cancel:
        return AppException.unknown(
          message: 'Request was cancelled',
          code: 'CANCELLED',
          userMessage: 'Request was cancelled',
          originalError: error,
          stackTrace: error.stackTrace,
        );

      case DioExceptionType.connectionError:
        return AppException.network(
          message: 'Connection error: ${error.message}',
          code: 'CONNECTION_ERROR',
          userMessage:
              'Unable to connect. Please check your internet connection.',
          originalError: error,
          stackTrace: error.stackTrace,
        );

      case DioExceptionType.badCertificate:
        return AppException.network(
          message: 'SSL certificate error: ${error.message}',
          code: 'SSL_ERROR',
          userMessage: 'Security certificate error. Please try again.',
          originalError: error,
          stackTrace: error.stackTrace,
        );

      case DioExceptionType.unknown:
        return AppException.unknown(
          message: 'Unknown network error: ${error.message}',
          code: 'UNKNOWN_NETWORK',
          userMessage: 'Network error occurred. Please try again.',
          originalError: error,
          stackTrace: error.stackTrace,
        );
    }
  }

  /// Handle HTTP response errors
  AppException _handleHttpError(DioException error) {
    final int? statusCode = error.response?.statusCode;
    final dynamic responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return _handleBadRequestError(error, responseData);
      case 401:
        return AppException.authentication(
          message: 'Unauthorized: ${error.message}',
          code: 'UNAUTHORIZED',
          userMessage: 'Authentication failed. Please login again.',
          originalError: error,
          stackTrace: error.stackTrace,
        );
      case 403:
        return AppException.authorization(
          message: 'Forbidden: ${error.message}',
          code: 'FORBIDDEN',
          userMessage:
              'Access denied. You don\'t have permission to perform this action.',
          originalError: error,
          stackTrace: error.stackTrace,
        );
      case 404:
        return AppException.server(
          message: 'Not found: ${error.message}',
          code: 'NOT_FOUND',
          userMessage: 'Requested resource not found.',
          statusCode: statusCode,
          originalError: error,
          stackTrace: error.stackTrace,
        );
      case 422:
        return _handleValidationError(error, responseData);
      case 429:
        return AppException.server(
          message: 'Too many requests: ${error.message}',
          code: 'RATE_LIMIT',
          userMessage: 'Too many requests. Please try again later.',
          statusCode: statusCode,
          originalError: error,
          stackTrace: error.stackTrace,
        );
      case 500:
      case 502:
      case 503:
      case 504:
        return AppException.server(
          message: 'Server error: ${error.message}',
          code: 'SERVER_ERROR',
          userMessage: 'Server is experiencing issues. Please try again later.',
          statusCode: statusCode,
          originalError: error,
          stackTrace: error.stackTrace,
        );
      default:
        return AppException.server(
          message: 'HTTP error $statusCode: ${error.message}',
          code: 'HTTP_ERROR',
          userMessage: 'Server error occurred. Please try again.',
          statusCode: statusCode,
          originalError: error,
          stackTrace: error.stackTrace,
        );
    }
  }

  /// Handle bad request errors (400)
  AppException _handleBadRequestError(
    DioException error,
    dynamic responseData,
  ) {
    if (responseData is Map<String, dynamic>) {
      final String? message = responseData['message'] as String?;
      final Map<String, dynamic>? errors =
          responseData['errors'] as Map<String, dynamic>?;

      Map<String, List<String>>? fieldErrors;
      if (errors != null) {
        fieldErrors = errors.map((key, value) {
          if (value is List) {
            return MapEntry(key, value.cast<String>());
          } else if (value is String) {
            return MapEntry(key, [value]);
          } else {
            return MapEntry(key, [value.toString()]);
          }
        });
      }

      return AppException.validation(
        message: message ?? 'Bad request: ${error.message}',
        code: 'BAD_REQUEST',
        userMessage: message ?? 'Invalid request. Please check your input.',
        fieldErrors: fieldErrors,
        originalError: error,
        stackTrace: error.stackTrace,
      );
    }

    return AppException.validation(
      message: 'Bad request: ${error.message}',
      code: 'BAD_REQUEST',
      userMessage: 'Invalid request. Please check your input.',
      originalError: error,
      stackTrace: error.stackTrace,
    );
  }

  /// Handle validation errors (422)
  AppException _handleValidationError(
    DioException error,
    dynamic responseData,
  ) {
    if (responseData is Map<String, dynamic>) {
      final String? message = responseData['message'] as String?;
      final Map<String, dynamic>? errors =
          responseData['errors'] as Map<String, dynamic>?;

      Map<String, List<String>>? fieldErrors;
      if (errors != null) {
        fieldErrors = errors.map((key, value) {
          if (value is List) {
            return MapEntry(key, value.cast<String>());
          } else if (value is String) {
            return MapEntry(key, [value]);
          } else {
            return MapEntry(key, [value.toString()]);
          }
        });
      }

      return AppException.validation(
        message: message ?? 'Validation failed: ${error.message}',
        code: 'VALIDATION_ERROR',
        userMessage: message ?? 'Please correct the highlighted fields.',
        fieldErrors: fieldErrors,
        originalError: error,
        stackTrace: error.stackTrace,
      );
    }

    return AppException.validation(
      message: 'Validation failed: ${error.message}',
      code: 'VALIDATION_ERROR',
      userMessage: 'Please correct the highlighted fields.',
      originalError: error,
      stackTrace: error.stackTrace,
    );
  }

  /// Handle socket errors
  AppException _handleSocketError(
    SocketException error,
    StackTrace? stackTrace,
  ) {
    return AppException.network(
      message: 'Socket error: ${error.message}',
      code: 'SOCKET_ERROR',
      userMessage: 'Unable to connect. Please check your internet connection.',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Handle timeout errors
  AppException _handleTimeoutError(
    TimeoutException error,
    StackTrace? stackTrace,
  ) {
    return AppException.timeout(
      message: 'Timeout error: ${error.message}',
      code: 'TIMEOUT',
      userMessage: 'Request timed out. Please try again.',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Handle format errors
  AppException _handleFormatError(
    FormatException error,
    StackTrace? stackTrace,
  ) {
    return AppException.unknown(
      message: 'Format error: ${error.message}',
      code: 'FORMAT_ERROR',
      userMessage: 'Data format error. Please try again.',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Handle unknown errors
  AppException _handleUnknownError(Object error, StackTrace? stackTrace) {
    return AppException.unknown(
      message: 'Unknown error: $error',
      code: 'UNKNOWN',
      userMessage: 'An unexpected error occurred. Please try again.',
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Dispose resources
  void dispose() {
    _errorController.close();
  }
}
