import 'dart:async';
import 'dart:convert';

import 'package:logger/logger.dart';

import 'storage_service.dart';

/// Service for managing JWT tokens including validation and refresh logic
class TokenService {
  TokenService({required StorageService storageService})
    : _storageService = storageService;

  final StorageService _storageService;
  final Logger _logger = Logger();

  /// Store authentication tokens
  Future<void> storeTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    await _storageService.storeAccessToken(accessToken);
    if (refreshToken != null) {
      await _storageService.storeRefreshToken(refreshToken);
    }
    _logger.i('Tokens stored successfully');
  }

  /// Get current access token
  Future<String?> getAccessToken() async {
    return await _storageService.getAccessToken();
  }

  /// Get current refresh token
  Future<String?> getRefreshToken() async {
    return await _storageService.getRefreshToken();
  }

  /// Check if user has valid access token
  Future<bool> hasValidAccessToken() async {
    final String? token = await getAccessToken();
    if (token == null || token.isEmpty) {
      return false;
    }

    // Check if token is expired
    return !isTokenExpired(token);
  }

  /// Check if a JWT token is expired
  bool isTokenExpired(String token) {
    try {
      final List<String> parts = token.split('.');
      if (parts.length != 3) {
        _logger.w('Invalid JWT token format');
        return true;
      }

      // Decode the payload (second part)
      final String payload = parts[1];
      final String normalizedPayload = base64Url.normalize(payload);
      final String decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      // Check expiration time
      final int? exp = payloadMap['exp'] as int?;
      if (exp == null) {
        _logger.w('Token does not contain expiration time');
        return true;
      }

      final DateTime expirationDate = DateTime.fromMillisecondsSinceEpoch(
        exp * 1000,
      );
      final DateTime now = DateTime.now();

      // Add 5 minutes buffer before expiration
      final DateTime bufferTime = expirationDate.subtract(
        const Duration(minutes: 5),
      );

      return now.isAfter(bufferTime);
    } catch (e) {
      _logger.e('Error checking token expiration: $e');
      return true;
    }
  }

  /// Get token expiration date
  DateTime? getTokenExpirationDate(String token) {
    try {
      final List<String> parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      final String payload = parts[1];
      final String normalizedPayload = base64Url.normalize(payload);
      final String decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      final int? exp = payloadMap['exp'] as int?;
      if (exp == null) {
        return null;
      }

      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      _logger.e('Error getting token expiration date: $e');
      return null;
    }
  }

  /// Get user ID from token
  String? getUserIdFromToken(String token) {
    try {
      final List<String> parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      final String payload = parts[1];
      final String normalizedPayload = base64Url.normalize(payload);
      final String decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      return payloadMap['sub'] as String?;
    } catch (e) {
      _logger.e('Error getting user ID from token: $e');
      return null;
    }
  }

  /// Get user email from token
  String? getUserEmailFromToken(String token) {
    try {
      final List<String> parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      final String payload = parts[1];
      final String normalizedPayload = base64Url.normalize(payload);
      final String decoded = utf8.decode(base64Url.decode(normalizedPayload));
      final Map<String, dynamic> payloadMap = jsonDecode(decoded);

      return payloadMap['email'] as String?;
    } catch (e) {
      _logger.e('Error getting user email from token: $e');
      return null;
    }
  }

  /// Clear all tokens (logout)
  Future<void> clearTokens() async {
    await _storageService.clearAuthData();
    _logger.i('Tokens cleared successfully');
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await hasValidAccessToken();
  }

  /// Get token payload as Map
  Map<String, dynamic>? getTokenPayload(String token) {
    try {
      final List<String> parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      final String payload = parts[1];
      final String normalizedPayload = base64Url.normalize(payload);
      final String decoded = utf8.decode(base64Url.decode(normalizedPayload));
      return jsonDecode(decoded) as Map<String, dynamic>;
    } catch (e) {
      _logger.e('Error getting token payload: $e');
      return null;
    }
  }

  /// Check if token needs refresh (expires in less than 10 minutes)
  bool shouldRefreshToken(String token) {
    try {
      final DateTime? expirationDate = getTokenExpirationDate(token);
      if (expirationDate == null) {
        return true;
      }

      final DateTime now = DateTime.now();
      final Duration timeUntilExpiration = expirationDate.difference(now);

      // Refresh if token expires in less than 10 minutes
      return timeUntilExpiration.inMinutes < 10;
    } catch (e) {
      _logger.e('Error checking if token should refresh: $e');
      return true;
    }
  }

  /// Refresh access token using refresh token
  Future<bool> refreshAccessToken() async {
    try {
      final String? refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        _logger.w('No refresh token available');
        return false;
      }

      // TODO: Implement API call to refresh token
      // This would typically call an API endpoint with the refresh token
      // For now, return false to indicate refresh failed
      _logger.w('Token refresh not implemented yet');
      return false;
    } catch (e) {
      _logger.e('Error refreshing access token: $e');
      return false;
    }
  }

  /// Auto refresh token if needed
  Future<bool> autoRefreshTokenIfNeeded() async {
    final String? token = await getAccessToken();
    if (token == null) return false;

    if (shouldRefreshToken(token)) {
      _logger.d('Token needs refresh, attempting auto-refresh');
      return await refreshAccessToken();
    }

    return true; // Token is still valid
  }

  /// Schedule automatic token refresh
  void scheduleTokenRefresh() {
    // Cancel existing timer
    _refreshTimer?.cancel();

    // Schedule refresh check every 5 minutes
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      final bool success = await autoRefreshTokenIfNeeded();
      if (!success) {
        _logger.w('Auto token refresh failed, canceling timer');
        timer.cancel();
      }
    });
  }

  /// Cancel automatic token refresh
  void cancelTokenRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  Timer? _refreshTimer;
}
