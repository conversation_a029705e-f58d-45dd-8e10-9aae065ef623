// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppException {
  String get message => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  String get userMessage => throw _privateConstructorUsedError;
  Object? get originalError => throw _privateConstructorUsedError;
  StackTrace? get stackTrace => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppExceptionCopyWith<AppException> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppExceptionCopyWith<$Res> {
  factory $AppExceptionCopyWith(
          AppException value, $Res Function(AppException) then) =
      _$AppExceptionCopyWithImpl<$Res, AppException>;
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class _$AppExceptionCopyWithImpl<$Res, $Val extends AppException>
    implements $AppExceptionCopyWith<$Res> {
  _$AppExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_value.copyWith(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NetworkExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$NetworkExceptionImplCopyWith(_$NetworkExceptionImpl value,
          $Res Function(_$NetworkExceptionImpl) then) =
      __$$NetworkExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$NetworkExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$NetworkExceptionImpl>
    implements _$$NetworkExceptionImplCopyWith<$Res> {
  __$$NetworkExceptionImplCopyWithImpl(_$NetworkExceptionImpl _value,
      $Res Function(_$NetworkExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$NetworkExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$NetworkExceptionImpl implements NetworkException {
  const _$NetworkExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Network error occurred',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.network(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkExceptionImplCopyWith<_$NetworkExceptionImpl> get copyWith =>
      __$$NetworkExceptionImplCopyWithImpl<_$NetworkExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return network(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return network?.call(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkException implements AppException {
  const factory NetworkException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$NetworkExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkExceptionImplCopyWith<_$NetworkExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticationExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$AuthenticationExceptionImplCopyWith(
          _$AuthenticationExceptionImpl value,
          $Res Function(_$AuthenticationExceptionImpl) then) =
      __$$AuthenticationExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$AuthenticationExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$AuthenticationExceptionImpl>
    implements _$$AuthenticationExceptionImplCopyWith<$Res> {
  __$$AuthenticationExceptionImplCopyWithImpl(
      _$AuthenticationExceptionImpl _value,
      $Res Function(_$AuthenticationExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$AuthenticationExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$AuthenticationExceptionImpl implements AuthenticationException {
  const _$AuthenticationExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Authentication failed',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.authentication(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationExceptionImplCopyWith<_$AuthenticationExceptionImpl>
      get copyWith => __$$AuthenticationExceptionImplCopyWithImpl<
          _$AuthenticationExceptionImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return authentication(
        message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return authentication?.call(
        message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(
          message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return authentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return authentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(this);
    }
    return orElse();
  }
}

abstract class AuthenticationException implements AppException {
  const factory AuthenticationException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$AuthenticationExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationExceptionImplCopyWith<_$AuthenticationExceptionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthorizationExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$AuthorizationExceptionImplCopyWith(
          _$AuthorizationExceptionImpl value,
          $Res Function(_$AuthorizationExceptionImpl) then) =
      __$$AuthorizationExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$AuthorizationExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$AuthorizationExceptionImpl>
    implements _$$AuthorizationExceptionImplCopyWith<$Res> {
  __$$AuthorizationExceptionImplCopyWithImpl(
      _$AuthorizationExceptionImpl _value,
      $Res Function(_$AuthorizationExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$AuthorizationExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$AuthorizationExceptionImpl implements AuthorizationException {
  const _$AuthorizationExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Access denied',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.authorization(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthorizationExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthorizationExceptionImplCopyWith<_$AuthorizationExceptionImpl>
      get copyWith => __$$AuthorizationExceptionImplCopyWithImpl<
          _$AuthorizationExceptionImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return authorization(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return authorization?.call(
        message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (authorization != null) {
      return authorization(
          message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return authorization(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return authorization?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (authorization != null) {
      return authorization(this);
    }
    return orElse();
  }
}

abstract class AuthorizationException implements AppException {
  const factory AuthorizationException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$AuthorizationExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthorizationExceptionImplCopyWith<_$AuthorizationExceptionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$ValidationExceptionImplCopyWith(_$ValidationExceptionImpl value,
          $Res Function(_$ValidationExceptionImpl) then) =
      __$$ValidationExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Map<String, List<String>>? fieldErrors,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$ValidationExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$ValidationExceptionImpl>
    implements _$$ValidationExceptionImplCopyWith<$Res> {
  __$$ValidationExceptionImplCopyWithImpl(_$ValidationExceptionImpl _value,
      $Res Function(_$ValidationExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? fieldErrors = freezed,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$ValidationExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      fieldErrors: freezed == fieldErrors
          ? _value._fieldErrors
          : fieldErrors // ignore: cast_nullable_to_non_nullable
              as Map<String, List<String>>?,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$ValidationExceptionImpl implements ValidationException {
  const _$ValidationExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Invalid input',
      final Map<String, List<String>>? fieldErrors,
      this.originalError,
      this.stackTrace})
      : _fieldErrors = fieldErrors;

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  final Map<String, List<String>>? _fieldErrors;
  @override
  Map<String, List<String>>? get fieldErrors {
    final value = _fieldErrors;
    if (value == null) return null;
    if (_fieldErrors is EqualUnmodifiableMapView) return _fieldErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.validation(message: $message, code: $code, userMessage: $userMessage, fieldErrors: $fieldErrors, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other._fieldErrors, _fieldErrors) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      code,
      userMessage,
      const DeepCollectionEquality().hash(_fieldErrors),
      const DeepCollectionEquality().hash(originalError),
      stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationExceptionImplCopyWith<_$ValidationExceptionImpl> get copyWith =>
      __$$ValidationExceptionImplCopyWithImpl<_$ValidationExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return validation(
        message, code, userMessage, fieldErrors, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return validation?.call(
        message, code, userMessage, fieldErrors, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(
          message, code, userMessage, fieldErrors, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationException implements AppException {
  const factory ValidationException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Map<String, List<String>>? fieldErrors,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$ValidationExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  Map<String, List<String>>? get fieldErrors;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationExceptionImplCopyWith<_$ValidationExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ServerExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$ServerExceptionImplCopyWith(_$ServerExceptionImpl value,
          $Res Function(_$ServerExceptionImpl) then) =
      __$$ServerExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      int? statusCode,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$ServerExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$ServerExceptionImpl>
    implements _$$ServerExceptionImplCopyWith<$Res> {
  __$$ServerExceptionImplCopyWithImpl(
      _$ServerExceptionImpl _value, $Res Function(_$ServerExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? statusCode = freezed,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$ServerExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int?,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$ServerExceptionImpl implements ServerException {
  const _$ServerExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Server error occurred',
      this.statusCode,
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final int? statusCode;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.server(message: $message, code: $code, userMessage: $userMessage, statusCode: $statusCode, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ServerExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      message,
      code,
      userMessage,
      statusCode,
      const DeepCollectionEquality().hash(originalError),
      stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ServerExceptionImplCopyWith<_$ServerExceptionImpl> get copyWith =>
      __$$ServerExceptionImplCopyWithImpl<_$ServerExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return server(
        message, code, userMessage, statusCode, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return server?.call(
        message, code, userMessage, statusCode, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(
          message, code, userMessage, statusCode, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return server(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return server?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (server != null) {
      return server(this);
    }
    return orElse();
  }
}

abstract class ServerException implements AppException {
  const factory ServerException(
      {required final String message,
      final String? code,
      final String userMessage,
      final int? statusCode,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$ServerExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  int? get statusCode;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ServerExceptionImplCopyWith<_$ServerExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TimeoutExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$TimeoutExceptionImplCopyWith(_$TimeoutExceptionImpl value,
          $Res Function(_$TimeoutExceptionImpl) then) =
      __$$TimeoutExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$TimeoutExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$TimeoutExceptionImpl>
    implements _$$TimeoutExceptionImplCopyWith<$Res> {
  __$$TimeoutExceptionImplCopyWithImpl(_$TimeoutExceptionImpl _value,
      $Res Function(_$TimeoutExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$TimeoutExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$TimeoutExceptionImpl implements TimeoutException {
  const _$TimeoutExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Request timed out',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.timeout(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeoutExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeoutExceptionImplCopyWith<_$TimeoutExceptionImpl> get copyWith =>
      __$$TimeoutExceptionImplCopyWithImpl<_$TimeoutExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return timeout(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return timeout?.call(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return timeout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return timeout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (timeout != null) {
      return timeout(this);
    }
    return orElse();
  }
}

abstract class TimeoutException implements AppException {
  const factory TimeoutException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$TimeoutExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeoutExceptionImplCopyWith<_$TimeoutExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$UnknownExceptionImplCopyWith(_$UnknownExceptionImpl value,
          $Res Function(_$UnknownExceptionImpl) then) =
      __$$UnknownExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$UnknownExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$UnknownExceptionImpl>
    implements _$$UnknownExceptionImplCopyWith<$Res> {
  __$$UnknownExceptionImplCopyWithImpl(_$UnknownExceptionImpl _value,
      $Res Function(_$UnknownExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$UnknownExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$UnknownExceptionImpl implements UnknownException {
  const _$UnknownExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'An unexpected error occurred',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.unknown(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownExceptionImplCopyWith<_$UnknownExceptionImpl> get copyWith =>
      __$$UnknownExceptionImplCopyWithImpl<_$UnknownExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return unknown(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return unknown?.call(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownException implements AppException {
  const factory UnknownException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$UnknownExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownExceptionImplCopyWith<_$UnknownExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StorageExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$StorageExceptionImplCopyWith(_$StorageExceptionImpl value,
          $Res Function(_$StorageExceptionImpl) then) =
      __$$StorageExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$StorageExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$StorageExceptionImpl>
    implements _$$StorageExceptionImplCopyWith<$Res> {
  __$$StorageExceptionImplCopyWithImpl(_$StorageExceptionImpl _value,
      $Res Function(_$StorageExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$StorageExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$StorageExceptionImpl implements StorageException {
  const _$StorageExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Storage error occurred',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.storage(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StorageExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StorageExceptionImplCopyWith<_$StorageExceptionImpl> get copyWith =>
      __$$StorageExceptionImplCopyWithImpl<_$StorageExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return storage(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return storage?.call(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return storage(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return storage?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (storage != null) {
      return storage(this);
    }
    return orElse();
  }
}

abstract class StorageException implements AppException {
  const factory StorageException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$StorageExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StorageExceptionImplCopyWith<_$StorageExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$PermissionExceptionImplCopyWith(_$PermissionExceptionImpl value,
          $Res Function(_$PermissionExceptionImpl) then) =
      __$$PermissionExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$PermissionExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$PermissionExceptionImpl>
    implements _$$PermissionExceptionImplCopyWith<$Res> {
  __$$PermissionExceptionImplCopyWithImpl(_$PermissionExceptionImpl _value,
      $Res Function(_$PermissionExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$PermissionExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$PermissionExceptionImpl implements PermissionException {
  const _$PermissionExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Permission denied',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.permission(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionExceptionImplCopyWith<_$PermissionExceptionImpl> get copyWith =>
      __$$PermissionExceptionImplCopyWithImpl<_$PermissionExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return permission(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return permission?.call(
        message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionException implements AppException {
  const factory PermissionException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$PermissionExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionExceptionImplCopyWith<_$PermissionExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LocationExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$LocationExceptionImplCopyWith(_$LocationExceptionImpl value,
          $Res Function(_$LocationExceptionImpl) then) =
      __$$LocationExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$LocationExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$LocationExceptionImpl>
    implements _$$LocationExceptionImplCopyWith<$Res> {
  __$$LocationExceptionImplCopyWithImpl(_$LocationExceptionImpl _value,
      $Res Function(_$LocationExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$LocationExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$LocationExceptionImpl implements LocationException {
  const _$LocationExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Location error occurred',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.location(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationExceptionImplCopyWith<_$LocationExceptionImpl> get copyWith =>
      __$$LocationExceptionImplCopyWithImpl<_$LocationExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return location(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return location?.call(
        message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (location != null) {
      return location(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return location(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return location?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (location != null) {
      return location(this);
    }
    return orElse();
  }
}

abstract class LocationException implements AppException {
  const factory LocationException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$LocationExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationExceptionImplCopyWith<_$LocationExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PaymentExceptionImplCopyWith<$Res>
    implements $AppExceptionCopyWith<$Res> {
  factory _$$PaymentExceptionImplCopyWith(_$PaymentExceptionImpl value,
          $Res Function(_$PaymentExceptionImpl) then) =
      __$$PaymentExceptionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String message,
      String? code,
      String userMessage,
      Object? originalError,
      StackTrace? stackTrace});
}

/// @nodoc
class __$$PaymentExceptionImplCopyWithImpl<$Res>
    extends _$AppExceptionCopyWithImpl<$Res, _$PaymentExceptionImpl>
    implements _$$PaymentExceptionImplCopyWith<$Res> {
  __$$PaymentExceptionImplCopyWithImpl(_$PaymentExceptionImpl _value,
      $Res Function(_$PaymentExceptionImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? code = freezed,
    Object? userMessage = null,
    Object? originalError = freezed,
    Object? stackTrace = freezed,
  }) {
    return _then(_$PaymentExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      userMessage: null == userMessage
          ? _value.userMessage
          : userMessage // ignore: cast_nullable_to_non_nullable
              as String,
      originalError:
          freezed == originalError ? _value.originalError : originalError,
      stackTrace: freezed == stackTrace
          ? _value.stackTrace
          : stackTrace // ignore: cast_nullable_to_non_nullable
              as StackTrace?,
    ));
  }
}

/// @nodoc

class _$PaymentExceptionImpl implements PaymentException {
  const _$PaymentExceptionImpl(
      {required this.message,
      this.code,
      this.userMessage = 'Payment error occurred',
      this.originalError,
      this.stackTrace});

  @override
  final String message;
  @override
  final String? code;
  @override
  @JsonKey()
  final String userMessage;
  @override
  final Object? originalError;
  @override
  final StackTrace? stackTrace;

  @override
  String toString() {
    return 'AppException.payment(message: $message, code: $code, userMessage: $userMessage, originalError: $originalError, stackTrace: $stackTrace)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.userMessage, userMessage) ||
                other.userMessage == userMessage) &&
            const DeepCollectionEquality()
                .equals(other.originalError, originalError) &&
            (identical(other.stackTrace, stackTrace) ||
                other.stackTrace == stackTrace));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code, userMessage,
      const DeepCollectionEquality().hash(originalError), stackTrace);

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentExceptionImplCopyWith<_$PaymentExceptionImpl> get copyWith =>
      __$$PaymentExceptionImplCopyWithImpl<_$PaymentExceptionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        network,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authentication,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        authorization,
    required TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)
        validation,
    required TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)
        server,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        timeout,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        unknown,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        storage,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        permission,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        location,
    required TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)
        payment,
  }) {
    return payment(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult? Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult? Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult? Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
  }) {
    return payment?.call(message, code, userMessage, originalError, stackTrace);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        network,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authentication,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        authorization,
    TResult Function(
            String message,
            String? code,
            String userMessage,
            Map<String, List<String>>? fieldErrors,
            Object? originalError,
            StackTrace? stackTrace)?
        validation,
    TResult Function(String message, String? code, String userMessage,
            int? statusCode, Object? originalError, StackTrace? stackTrace)?
        server,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        timeout,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        unknown,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        storage,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        permission,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        location,
    TResult Function(String message, String? code, String userMessage,
            Object? originalError, StackTrace? stackTrace)?
        payment,
    required TResult orElse(),
  }) {
    if (payment != null) {
      return payment(message, code, userMessage, originalError, stackTrace);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkException value) network,
    required TResult Function(AuthenticationException value) authentication,
    required TResult Function(AuthorizationException value) authorization,
    required TResult Function(ValidationException value) validation,
    required TResult Function(ServerException value) server,
    required TResult Function(TimeoutException value) timeout,
    required TResult Function(UnknownException value) unknown,
    required TResult Function(StorageException value) storage,
    required TResult Function(PermissionException value) permission,
    required TResult Function(LocationException value) location,
    required TResult Function(PaymentException value) payment,
  }) {
    return payment(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkException value)? network,
    TResult? Function(AuthenticationException value)? authentication,
    TResult? Function(AuthorizationException value)? authorization,
    TResult? Function(ValidationException value)? validation,
    TResult? Function(ServerException value)? server,
    TResult? Function(TimeoutException value)? timeout,
    TResult? Function(UnknownException value)? unknown,
    TResult? Function(StorageException value)? storage,
    TResult? Function(PermissionException value)? permission,
    TResult? Function(LocationException value)? location,
    TResult? Function(PaymentException value)? payment,
  }) {
    return payment?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkException value)? network,
    TResult Function(AuthenticationException value)? authentication,
    TResult Function(AuthorizationException value)? authorization,
    TResult Function(ValidationException value)? validation,
    TResult Function(ServerException value)? server,
    TResult Function(TimeoutException value)? timeout,
    TResult Function(UnknownException value)? unknown,
    TResult Function(StorageException value)? storage,
    TResult Function(PermissionException value)? permission,
    TResult Function(LocationException value)? location,
    TResult Function(PaymentException value)? payment,
    required TResult orElse(),
  }) {
    if (payment != null) {
      return payment(this);
    }
    return orElse();
  }
}

abstract class PaymentException implements AppException {
  const factory PaymentException(
      {required final String message,
      final String? code,
      final String userMessage,
      final Object? originalError,
      final StackTrace? stackTrace}) = _$PaymentExceptionImpl;

  @override
  String get message;
  @override
  String? get code;
  @override
  String get userMessage;
  @override
  Object? get originalError;
  @override
  StackTrace? get stackTrace;

  /// Create a copy of AppException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentExceptionImplCopyWith<_$PaymentExceptionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
