/// Types of API exceptions that can occur
enum ApiExceptionType {
  /// Network-related errors (timeout, no connection, etc.)
  network,
  
  /// Authentication errors (401)
  authentication,
  
  /// Authorization errors (403)
  authorization,
  
  /// Validation errors (400, 422)
  validation,
  
  /// Resource not found (404)
  notFound,
  
  /// Rate limiting (429)
  rateLimit,
  
  /// Server errors (5xx)
  server,
  
  /// Security-related errors (certificate, etc.)
  security,
  
  /// Request was cancelled
  cancelled,
  
  /// Unknown or unexpected errors
  unknown,
}

/// Custom exception class for API-related errors
class ApiException implements Exception {
  const ApiException({
    required this.message,
    this.type = ApiExceptionType.unknown,
    this.statusCode,
    this.context,
    this.originalError,
    this.stackTrace,
  });

  /// Human-readable error message
  final String message;
  
  /// Type of the exception
  final ApiExceptionType type;
  
  /// HTTP status code (if applicable)
  final int? statusCode;
  
  /// Context where the error occurred (e.g., method name)
  final String? context;
  
  /// Original error that caused this exception
  final dynamic originalError;
  
  /// Stack trace of the error
  final StackTrace? stackTrace;

  /// Check if this is a network-related error
  bool get isNetworkError => type == ApiExceptionType.network;
  
  /// Check if this is an authentication error
  bool get isAuthenticationError => type == ApiExceptionType.authentication;
  
  /// Check if this is an authorization error
  bool get isAuthorizationError => type == ApiExceptionType.authorization;
  
  /// Check if this is a validation error
  bool get isValidationError => type == ApiExceptionType.validation;
  
  /// Check if this is a server error
  bool get isServerError => type == ApiExceptionType.server;
  
  /// Check if this is a not found error
  bool get isNotFoundError => type == ApiExceptionType.notFound;
  
  /// Check if this is a rate limit error
  bool get isRateLimitError => type == ApiExceptionType.rateLimit;
  
  /// Check if the request was cancelled
  bool get isCancelledError => type == ApiExceptionType.cancelled;

  /// Get user-friendly error message based on type
  String get userFriendlyMessage {
    switch (type) {
      case ApiExceptionType.network:
        return 'Please check your internet connection and try again.';
      case ApiExceptionType.authentication:
        return 'Please log in again to continue.';
      case ApiExceptionType.authorization:
        return 'You don\'t have permission to perform this action.';
      case ApiExceptionType.validation:
        return message; // Validation messages are usually user-friendly
      case ApiExceptionType.notFound:
        return 'The requested information could not be found.';
      case ApiExceptionType.rateLimit:
        return 'Too many requests. Please wait a moment and try again.';
      case ApiExceptionType.server:
        return 'Server is temporarily unavailable. Please try again later.';
      case ApiExceptionType.security:
        return 'A security error occurred. Please try again.';
      case ApiExceptionType.cancelled:
        return 'Request was cancelled.';
      case ApiExceptionType.unknown:
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Create a copy of this exception with updated properties
  ApiException copyWith({
    String? message,
    ApiExceptionType? type,
    int? statusCode,
    String? context,
    dynamic originalError,
    StackTrace? stackTrace,
  }) {
    return ApiException(
      message: message ?? this.message,
      type: type ?? this.type,
      statusCode: statusCode ?? this.statusCode,
      context: context ?? this.context,
      originalError: originalError ?? this.originalError,
      stackTrace: stackTrace ?? this.stackTrace,
    );
  }

  @override
  String toString() {
    final StringBuffer buffer = StringBuffer();
    buffer.write('ApiException: $message');
    
    if (context != null) {
      buffer.write(' (Context: $context)');
    }
    
    if (statusCode != null) {
      buffer.write(' (Status: $statusCode)');
    }
    
    buffer.write(' (Type: $type)');
    
    if (originalError != null) {
      buffer.write(' (Original: $originalError)');
    }
    
    return buffer.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ApiException &&
        other.message == message &&
        other.type == type &&
        other.statusCode == statusCode &&
        other.context == context;
  }

  @override
  int get hashCode {
    return Object.hash(
      message,
      type,
      statusCode,
      context,
    );
  }
}

/// Exception for authentication-related errors
class AuthenticationException extends ApiException {
  const AuthenticationException({
    String message = 'Authentication failed',
    String? context,
    dynamic originalError,
  }) : super(
          message: message,
          type: ApiExceptionType.authentication,
          statusCode: 401,
          context: context,
          originalError: originalError,
        );
}

/// Exception for authorization-related errors
class AuthorizationException extends ApiException {
  const AuthorizationException({
    String message = 'Access denied',
    String? context,
    dynamic originalError,
  }) : super(
          message: message,
          type: ApiExceptionType.authorization,
          statusCode: 403,
          context: context,
          originalError: originalError,
        );
}

/// Exception for validation errors
class ValidationException extends ApiException {
  const ValidationException({
    required String message,
    String? context,
    dynamic originalError,
  }) : super(
          message: message,
          type: ApiExceptionType.validation,
          statusCode: 400,
          context: context,
          originalError: originalError,
        );
}

/// Exception for network-related errors
class NetworkException extends ApiException {
  const NetworkException({
    String message = 'Network error occurred',
    String? context,
    dynamic originalError,
  }) : super(
          message: message,
          type: ApiExceptionType.network,
          context: context,
          originalError: originalError,
        );
}
