import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_exception.freezed.dart';

/// Base application exception class
@freezed
class AppException with _$AppException implements Exception {
  const factory AppException.network({
    required String message,
    String? code,
    @Default('Network error occurred') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = NetworkException;

  const factory AppException.authentication({
    required String message,
    String? code,
    @Default('Authentication failed') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = AuthenticationException;

  const factory AppException.authorization({
    required String message,
    String? code,
    @Default('Access denied') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = AuthorizationException;

  const factory AppException.validation({
    required String message,
    String? code,
    @Default('Invalid input') String userMessage,
    Map<String, List<String>>? fieldErrors,
    Object? originalError,
    StackTrace? stackTrace,
  }) = ValidationException;

  const factory AppException.server({
    required String message,
    String? code,
    @Default('Server error occurred') String userMessage,
    int? statusCode,
    Object? originalError,
    StackTrace? stackTrace,
  }) = ServerException;

  const factory AppException.timeout({
    required String message,
    String? code,
    @Default('Request timed out') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = TimeoutException;

  const factory AppException.unknown({
    required String message,
    String? code,
    @Default('An unexpected error occurred') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = UnknownException;

  const factory AppException.storage({
    required String message,
    String? code,
    @Default('Storage error occurred') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = StorageException;

  const factory AppException.permission({
    required String message,
    String? code,
    @Default('Permission denied') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = PermissionException;

  const factory AppException.location({
    required String message,
    String? code,
    @Default('Location error occurred') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = LocationException;

  const factory AppException.payment({
    required String message,
    String? code,
    @Default('Payment error occurred') String userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) = PaymentException;
}

/// Extension methods for AppException
extension AppExceptionExtension on AppException {
  /// Get user-friendly message
  String get userFriendlyMessage {
    return when(
      network: (message, code, userMessage, originalError, stackTrace) => userMessage,
      authentication: (message, code, userMessage, originalError, stackTrace) => userMessage,
      authorization: (message, code, userMessage, originalError, stackTrace) => userMessage,
      validation: (message, code, userMessage, fieldErrors, originalError, stackTrace) => userMessage,
      server: (message, code, userMessage, statusCode, originalError, stackTrace) => userMessage,
      timeout: (message, code, userMessage, originalError, stackTrace) => userMessage,
      unknown: (message, code, userMessage, originalError, stackTrace) => userMessage,
      storage: (message, code, userMessage, originalError, stackTrace) => userMessage,
      permission: (message, code, userMessage, originalError, stackTrace) => userMessage,
      location: (message, code, userMessage, originalError, stackTrace) => userMessage,
      payment: (message, code, userMessage, originalError, stackTrace) => userMessage,
    );
  }

  /// Get error code
  String? get errorCode {
    return when(
      network: (message, code, userMessage, originalError, stackTrace) => code,
      authentication: (message, code, userMessage, originalError, stackTrace) => code,
      authorization: (message, code, userMessage, originalError, stackTrace) => code,
      validation: (message, code, userMessage, fieldErrors, originalError, stackTrace) => code,
      server: (message, code, userMessage, statusCode, originalError, stackTrace) => code,
      timeout: (message, code, userMessage, originalError, stackTrace) => code,
      unknown: (message, code, userMessage, originalError, stackTrace) => code,
      storage: (message, code, userMessage, originalError, stackTrace) => code,
      permission: (message, code, userMessage, originalError, stackTrace) => code,
      location: (message, code, userMessage, originalError, stackTrace) => code,
      payment: (message, code, userMessage, originalError, stackTrace) => code,
    );
  }

  /// Check if exception is recoverable
  bool get isRecoverable {
    return when(
      network: (message, code, userMessage, originalError, stackTrace) => true,
      authentication: (message, code, userMessage, originalError, stackTrace) => true,
      authorization: (message, code, userMessage, originalError, stackTrace) => false,
      validation: (message, code, userMessage, fieldErrors, originalError, stackTrace) => true,
      server: (message, code, userMessage, statusCode, originalError, stackTrace) => statusCode != 500,
      timeout: (message, code, userMessage, originalError, stackTrace) => true,
      unknown: (message, code, userMessage, originalError, stackTrace) => false,
      storage: (message, code, userMessage, originalError, stackTrace) => true,
      permission: (message, code, userMessage, originalError, stackTrace) => false,
      location: (message, code, userMessage, originalError, stackTrace) => true,
      payment: (message, code, userMessage, originalError, stackTrace) => true,
    );
  }

  /// Get suggested action for the user
  String get suggestedAction {
    return when(
      network: (message, code, userMessage, originalError, stackTrace) => 
          'Please check your internet connection and try again.',
      authentication: (message, code, userMessage, originalError, stackTrace) => 
          'Please check your credentials and try again.',
      authorization: (message, code, userMessage, originalError, stackTrace) => 
          'You don\'t have permission to perform this action.',
      validation: (message, code, userMessage, fieldErrors, originalError, stackTrace) => 
          'Please correct the highlighted fields and try again.',
      server: (message, code, userMessage, statusCode, originalError, stackTrace) => 
          'Server is experiencing issues. Please try again later.',
      timeout: (message, code, userMessage, originalError, stackTrace) => 
          'Request timed out. Please try again.',
      unknown: (message, code, userMessage, originalError, stackTrace) => 
          'Please try again or contact support if the problem persists.',
      storage: (message, code, userMessage, originalError, stackTrace) => 
          'Storage error occurred. Please try again.',
      permission: (message, code, userMessage, originalError, stackTrace) => 
          'Please grant the required permissions in settings.',
      location: (message, code, userMessage, originalError, stackTrace) => 
          'Please enable location services and try again.',
      payment: (message, code, userMessage, originalError, stackTrace) => 
          'Payment failed. Please check your payment method and try again.',
    );
  }
}

/// Exception factory for creating common exceptions
class ExceptionFactory {
  /// Create network exception
  static AppException network({
    required String message,
    String? code,
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.network(
      message: message,
      code: code,
      userMessage: userMessage ?? 'Network error occurred',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create authentication exception
  static AppException authentication({
    required String message,
    String? code,
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.authentication(
      message: message,
      code: code,
      userMessage: userMessage ?? 'Authentication failed',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create validation exception
  static AppException validation({
    required String message,
    String? code,
    String? userMessage,
    Map<String, List<String>>? fieldErrors,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.validation(
      message: message,
      code: code,
      userMessage: userMessage ?? 'Invalid input',
      fieldErrors: fieldErrors,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create server exception
  static AppException server({
    required String message,
    String? code,
    String? userMessage,
    int? statusCode,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.server(
      message: message,
      code: code,
      userMessage: userMessage ?? 'Server error occurred',
      statusCode: statusCode,
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create timeout exception
  static AppException timeout({
    required String message,
    String? code,
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.timeout(
      message: message,
      code: code,
      userMessage: userMessage ?? 'Request timed out',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }

  /// Create unknown exception
  static AppException unknown({
    required String message,
    String? code,
    String? userMessage,
    Object? originalError,
    StackTrace? stackTrace,
  }) {
    return AppException.unknown(
      message: message,
      code: code,
      userMessage: userMessage ?? 'An unexpected error occurred',
      originalError: originalError,
      stackTrace: stackTrace,
    );
  }
}
