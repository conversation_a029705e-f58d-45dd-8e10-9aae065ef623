import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../api/api_client.dart';
import '../api/auth_api.dart';
import '../api/interceptors/auth_interceptor.dart';
import '../api/interceptors/logging_interceptor.dart';
import '../exceptions/api_exception.dart';
import '../repositories/auth_repository.dart';
import '../services/auth_service.dart';
import '../services/error_handler.dart';
import '../services/session_manager.dart';
import '../services/storage_service.dart';
import '../services/token_service.dart';

/// Global service locator instance
final GetIt getIt = GetIt.instance;

/// Initialize all dependencies for the application
/// This should be called once at app startup
Future<void> initializeDependencies() async {
  // External dependencies
  final SharedPreferences sharedPreferences =
      await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  const FlutterSecureStorage secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );
  getIt.registerSingleton<FlutterSecureStorage>(secureStorage);

  // Core services
  getIt.registerLazySingleton<StorageService>(
    () => StorageService(
      secureStorage: getIt<FlutterSecureStorage>(),
      sharedPreferences: getIt<SharedPreferences>(),
    ),
  );

  getIt.registerLazySingleton<TokenService>(
    () => TokenService(storageService: getIt<StorageService>()),
  );

  // HTTP client and interceptors
  final Dio dio = Dio();

  // Add logging interceptor
  dio.interceptors.add(LoggingInterceptor());

  // Add auth interceptor
  dio.interceptors.add(AuthInterceptor(tokenService: getIt<TokenService>()));

  getIt.registerSingleton<Dio>(dio);

  // API clients
  getIt.registerLazySingleton<ApiClient>(() => ApiClient(dio: getIt<Dio>()));

  getIt.registerLazySingleton<AuthApi>(() => AuthApi(getIt<Dio>()));

  // Repositories
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepository(
      authApi: getIt<AuthApi>(),
      tokenService: getIt<TokenService>(),
      dio: getIt<Dio>(),
    ),
  );

  // Services
  getIt.registerLazySingleton<AuthService>(
    () => AuthService(
      authRepository: getIt<AuthRepository>(),
      tokenService: getIt<TokenService>(),
    ),
  );

  getIt.registerLazySingleton<SessionManager>(
    () => SessionManager(
      storageService: getIt<StorageService>(),
      tokenService: getIt<TokenService>(),
    ),
  );

  getIt.registerLazySingleton<SessionWarningService>(
    () => SessionWarningService(
      sessionManager: getIt<SessionManager>(),
      storageService: getIt<StorageService>(),
    ),
  );

  getIt.registerLazySingleton<BiometricAuthService>(
    () => BiometricAuthService(storageService: getIt<StorageService>()),
  );

  getIt.registerLazySingleton<ErrorHandler>(() => ErrorHandler());
}

/// Reset all dependencies (useful for testing)
Future<void> resetDependencies() async {
  await getIt.reset();
}
