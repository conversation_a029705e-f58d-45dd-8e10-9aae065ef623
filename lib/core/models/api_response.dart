import '../exceptions/api_exception.dart';

/// Generic wrapper for API responses that can represent success or error states
class ApiResponse<T> {
  const ApiResponse._({
    this.data,
    this.error,
    required this.isSuccess,
  });

  /// The response data (only available on success)
  final T? data;
  
  /// The error information (only available on failure)
  final ApiException? error;
  
  /// Whether the response represents a successful operation
  final bool isSuccess;

  /// Create a successful response
  factory ApiResponse.success(T data) {
    return ApiResponse._(
      data: data,
      isSuccess: true,
    );
  }

  /// Create an error response
  factory ApiResponse.error(ApiException error) {
    return ApiResponse._(
      error: error,
      isSuccess: false,
    );
  }

  /// Whether the response represents a failed operation
  bool get isError => !isSuccess;

  /// Get the data or throw if error
  T get dataOrThrow {
    if (isSuccess && data != null) {
      return data!;
    } else if (error != null) {
      throw error!;
    } else {
      throw const ApiException(message: 'No data available');
    }
  }

  /// Get the data or return null if error
  T? get dataOrNull => isSuccess ? data : null;

  /// Get the error message or null if success
  String? get errorMessage => error?.message;

  /// Get user-friendly error message or null if success
  String? get userFriendlyErrorMessage => error?.userFriendlyMessage;

  /// Transform the data if successful, otherwise return error response
  ApiResponse<R> map<R>(R Function(T data) transform) {
    if (isSuccess && data != null) {
      try {
        return ApiResponse.success(transform(data!));
      } catch (e) {
        return ApiResponse.error(
          ApiException(
            message: 'Error transforming data: $e',
            originalError: e,
          ),
        );
      }
    } else {
      return ApiResponse.error(error!);
    }
  }

  /// Transform the data asynchronously if successful
  Future<ApiResponse<R>> mapAsync<R>(Future<R> Function(T data) transform) async {
    if (isSuccess && data != null) {
      try {
        final R result = await transform(data!);
        return ApiResponse.success(result);
      } catch (e) {
        return ApiResponse.error(
          ApiException(
            message: 'Error transforming data: $e',
            originalError: e,
          ),
        );
      }
    } else {
      return ApiResponse.error(error!);
    }
  }

  /// Execute a function if successful, otherwise do nothing
  void ifSuccess(void Function(T data) action) {
    if (isSuccess && data != null) {
      action(data!);
    }
  }

  /// Execute a function if error, otherwise do nothing
  void ifError(void Function(ApiException error) action) {
    if (isError && error != null) {
      action(error!);
    }
  }

  /// Execute different functions based on success or error state
  R when<R>({
    required R Function(T data) success,
    required R Function(ApiException error) error,
  }) {
    if (isSuccess && data != null) {
      return success(data!);
    } else if (this.error != null) {
      return error(this.error!);
    } else {
      return error(const ApiException(message: 'Unknown error'));
    }
  }

  /// Execute different async functions based on success or error state
  Future<R> whenAsync<R>({
    required Future<R> Function(T data) success,
    required Future<R> Function(ApiException error) error,
  }) async {
    if (isSuccess && data != null) {
      return await success(data!);
    } else if (this.error != null) {
      return await error(this.error!);
    } else {
      return await error(const ApiException(message: 'Unknown error'));
    }
  }

  /// Fold the response into a single value
  R fold<R>(
    R Function(ApiException error) onError,
    R Function(T data) onSuccess,
  ) {
    if (isSuccess && data != null) {
      return onSuccess(data!);
    } else if (error != null) {
      return onError(error!);
    } else {
      return onError(const ApiException(message: 'Unknown error'));
    }
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'ApiResponse.success(data: $data)';
    } else {
      return 'ApiResponse.error(error: $error)';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ApiResponse<T> &&
        other.data == data &&
        other.error == error &&
        other.isSuccess == isSuccess;
  }

  @override
  int get hashCode {
    return Object.hash(data, error, isSuccess);
  }
}
