import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../../services/token_service.dart';

/// Interceptor for handling authentication tokens in HTTP requests
class AuthInterceptor extends Interceptor {
  AuthInterceptor({required TokenService tokenService})
    : _tokenService = tokenService;

  final TokenService _tokenService;
  final Logger _logger = Logger();

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      // Skip adding token for auth endpoints
      if (_isAuthEndpoint(options.path)) {
        handler.next(options);
        return;
      }

      // Get access token
      final String? accessToken = await _tokenService.getAccessToken();

      if (accessToken != null && accessToken.isNotEmpty) {
        // Check if token is expired or needs refresh
        if (_tokenService.isTokenExpired(accessToken)) {
          _logger.w('Access token is expired');
          // TODO: Implement token refresh logic here
          // For now, continue with expired token and let the server handle it
        }

        // Add Authorization header
        options.headers['Authorization'] = 'Bearer $accessToken';
        _logger.d('Added Authorization header to request: ${options.path}');
      } else {
        _logger.d('No access token available for request: ${options.path}');
      }

      handler.next(options);
    } catch (e) {
      _logger.e('Error in AuthInterceptor onRequest: $e');
      handler.next(options);
    }
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    _logger.d(
      'Response received: ${response.statusCode} ${response.requestOptions.path}',
    );
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    _logger.e(
      'HTTP Error: ${err.response?.statusCode} ${err.requestOptions.path}',
    );

    // Handle 401 Unauthorized errors
    if (err.response?.statusCode == 401) {
      await _handle401Error(err, handler);
      return;
    }

    handler.next(err);
  }

  /// Handle 401 Unauthorized errors
  Future<void> _handle401Error(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    try {
      // Skip token refresh for auth endpoints
      if (_isAuthEndpoint(err.requestOptions.path)) {
        handler.next(err);
        return;
      }

      final String? refreshToken = await _tokenService.getRefreshToken();

      if (refreshToken == null || refreshToken.isEmpty) {
        _logger.w('No refresh token available, clearing tokens');
        await _tokenService.clearTokens();
        handler.next(err);
        return;
      }

      // TODO: Implement token refresh logic
      // For now, just clear tokens and let the user re-authenticate
      _logger.w('Token refresh not implemented yet, clearing tokens');
      await _tokenService.clearTokens();
      handler.next(err);
    } catch (e) {
      _logger.e('Error handling 401 error: $e');
      await _tokenService.clearTokens();
      handler.next(err);
    }
  }

  /// Check if the endpoint is an authentication endpoint
  bool _isAuthEndpoint(String path) {
    final List<String> authEndpoints = [
      '/auth/login',
      '/auth/register',
      '/auth/refresh',
      '/auth/logout',
    ];

    return authEndpoints.any((endpoint) => path.contains(endpoint));
  }

  /// Retry the original request with new token
  Future<Response<dynamic>> _retryRequest(
    RequestOptions requestOptions,
    String newAccessToken,
  ) async {
    // Update the authorization header
    requestOptions.headers['Authorization'] = 'Bearer $newAccessToken';

    // Create a new Dio instance to avoid interceptor loops
    final Dio dio = Dio();
    dio.options.baseUrl = requestOptions.baseUrl;
    dio.options.connectTimeout = requestOptions.connectTimeout;
    dio.options.receiveTimeout = requestOptions.receiveTimeout;
    dio.options.sendTimeout = requestOptions.sendTimeout;

    try {
      final Response<dynamic> response = await dio.request(
        requestOptions.path,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        options: Options(
          method: requestOptions.method,
          headers: requestOptions.headers,
          responseType: requestOptions.responseType,
          contentType: requestOptions.contentType,
          validateStatus: requestOptions.validateStatus,
          receiveTimeout: requestOptions.receiveTimeout,
          sendTimeout: requestOptions.sendTimeout,
        ),
      );

      _logger.i('Request retried successfully: ${requestOptions.path}');
      return response;
    } catch (e) {
      _logger.e('Error retrying request: $e');
      rethrow;
    }
  }
}
