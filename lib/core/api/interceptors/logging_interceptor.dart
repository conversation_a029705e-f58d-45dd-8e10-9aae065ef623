import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

/// Interceptor for logging HTTP requests and responses
class LoggingInterceptor extends Interceptor {
  LoggingInterceptor({
    this.logRequests = true,
    this.logResponses = true,
    this.logErrors = true,
    this.logHeaders = false,
    this.logRequestBody = true,
    this.logResponseBody = false,
  });

  final bool logRequests;
  final bool logResponses;
  final bool logErrors;
  final bool logHeaders;
  final bool logRequestBody;
  final bool logResponseBody;

  final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 5,
      lineLength: 80,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
  );

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (logRequests) {
      _logRequest(options);
    }
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (logResponses) {
      _logResponse(response);
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (logErrors) {
      _logError(err);
    }
    handler.next(err);
  }

  void _logRequest(RequestOptions options) {
    final StringBuffer buffer = StringBuffer();
    
    buffer.writeln('🚀 REQUEST');
    buffer.writeln('Method: ${options.method}');
    buffer.writeln('URL: ${options.uri}');
    
    if (logHeaders && options.headers.isNotEmpty) {
      buffer.writeln('Headers:');
      options.headers.forEach((key, value) {
        // Hide sensitive headers
        if (_isSensitiveHeader(key)) {
          buffer.writeln('  $key: [HIDDEN]');
        } else {
          buffer.writeln('  $key: $value');
        }
      });
    }

    if (logRequestBody && options.data != null) {
      buffer.writeln('Body: ${_formatData(options.data)}');
    }

    if (options.queryParameters.isNotEmpty) {
      buffer.writeln('Query Parameters: ${options.queryParameters}');
    }

    _logger.i(buffer.toString());
  }

  void _logResponse(Response response) {
    final StringBuffer buffer = StringBuffer();
    
    buffer.writeln('✅ RESPONSE');
    buffer.writeln('Status: ${response.statusCode} ${response.statusMessage}');
    buffer.writeln('URL: ${response.requestOptions.uri}');
    
    if (logHeaders && response.headers.map.isNotEmpty) {
      buffer.writeln('Headers:');
      response.headers.map.forEach((key, value) {
        buffer.writeln('  $key: ${value.join(', ')}');
      });
    }

    if (logResponseBody && response.data != null) {
      buffer.writeln('Body: ${_formatData(response.data)}');
    }

    _logger.i(buffer.toString());
  }

  void _logError(DioException err) {
    final StringBuffer buffer = StringBuffer();
    
    buffer.writeln('❌ ERROR');
    buffer.writeln('Type: ${err.type}');
    buffer.writeln('Message: ${err.message}');
    buffer.writeln('URL: ${err.requestOptions.uri}');
    
    if (err.response != null) {
      buffer.writeln('Status: ${err.response!.statusCode} ${err.response!.statusMessage}');
      
      if (err.response!.data != null) {
        buffer.writeln('Error Body: ${_formatData(err.response!.data)}');
      }
    }

    if (err.stackTrace != null) {
      buffer.writeln('Stack Trace: ${err.stackTrace}');
    }

    _logger.e(buffer.toString());
  }

  String _formatData(dynamic data) {
    if (data == null) return 'null';
    
    if (data is String) {
      return data.length > 1000 ? '${data.substring(0, 1000)}...[truncated]' : data;
    }
    
    if (data is Map || data is List) {
      final String jsonString = data.toString();
      return jsonString.length > 1000 ? '${jsonString.substring(0, 1000)}...[truncated]' : jsonString;
    }
    
    return data.toString();
  }

  bool _isSensitiveHeader(String headerName) {
    final List<String> sensitiveHeaders = [
      'authorization',
      'cookie',
      'set-cookie',
      'x-api-key',
      'x-auth-token',
    ];
    
    return sensitiveHeaders.contains(headerName.toLowerCase());
  }
}
