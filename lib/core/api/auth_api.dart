import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api.g.dart';

/// Authentication API endpoints using Retrofit
@RestApi()
abstract class AuthApi {
  factory AuthApi(Dio dio) = _AuthApi;

  /// Register a new user
  @POST('/auth/register')
  Future<Response<Map<String, dynamic>>> register(
    @Body() Map<String, dynamic> userData,
  );

  /// Login user
  @POST('/auth/login')
  Future<Response<Map<String, dynamic>>> login(
    @Body() Map<String, dynamic> loginData,
  );

  /// Logout user
  @POST('/auth/logout')
  Future<Response<Map<String, dynamic>>> logout();

  /// Get current user profile
  @GET('/auth/profile')
  Future<Response<Map<String, dynamic>>> getProfile();

  /// Update user profile
  @PUT('/auth/profile')
  Future<Response<Map<String, dynamic>>> updateProfile(
    @Body() Map<String, dynamic> updateData,
  );

  /// Get complete user profile with type-specific data
  @GET('/auth/profile/complete')
  Future<Response<Map<String, dynamic>>> getCompleteProfile();

  /// Upload profile image (handled separately due to MultipartFile issues)
  // @POST('/auth/profile/image')
  // @MultiPart()
  // Future<Response<Map<String, dynamic>>> uploadProfileImage(
  //   @Part(name: "file") MultipartFile file,
  // );

  /// Delete profile image
  @DELETE('/auth/profile/image')
  Future<Response<Map<String, dynamic>>> deleteProfileImage();

  /// Get profile image info
  @GET('/auth/profile/image/info')
  Future<Response<Map<String, dynamic>>> getProfileImageInfo();

  /// Verify JWT token
  @GET('/auth/verify-token')
  Future<Response<Map<String, dynamic>>> verifyToken();

  /// Get current user info (alias for getProfile)
  @GET('/auth/me')
  Future<Response<Map<String, dynamic>>> getCurrentUserInfo();

  /// Deactivate user account
  @POST('/auth/deactivate')
  Future<Response<Map<String, dynamic>>> deactivateAccount();

  /// Get user type
  @GET('/auth/user-type')
  Future<Response<Map<String, dynamic>>> getUserType();

  /// Get user permissions
  @GET('/auth/permissions')
  Future<Response<Map<String, dynamic>>> getUserPermissions();

  /// Get profile completeness status
  @GET('/auth/profile/completeness')
  Future<Response<Map<String, dynamic>>> getProfileCompleteness();

  /// Get profile validation info
  @GET('/auth/profile/validation')
  Future<Response<Map<String, dynamic>>> getProfileValidation();

  /// Get token info (debug endpoint)
  @GET('/auth/debug/token-info')
  Future<Response<Map<String, dynamic>>> getTokenInfo();

  /// Health check
  @GET('/auth/health')
  Future<Response<Map<String, dynamic>>> healthCheck();
}
