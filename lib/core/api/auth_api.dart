import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'auth_api.g.dart';

/// Authentication API endpoints using Retrofit
@RestApi()
abstract class AuthApi {
  factory AuthApi(Dio dio) = _AuthApi;

  /// Register a new user
  @POST('/auth/register')
  Future<Map<String, dynamic>> register(@Body() Map<String, dynamic> userData);

  /// Login user
  @POST('/auth/login')
  Future<Map<String, dynamic>> login(@Body() Map<String, dynamic> loginData);

  /// Logout user
  @POST('/auth/logout')
  Future<Map<String, dynamic>> logout();

  /// Get current user profile
  @GET('/auth/profile')
  Future<Map<String, dynamic>> getProfile();

  /// Update user profile
  @PUT('/auth/profile')
  Future<Map<String, dynamic>> updateProfile(
    @Body() Map<String, dynamic> updateData,
  );

  /// Get complete user profile with type-specific data
  @GET('/auth/profile/complete')
  Future<Map<String, dynamic>> getCompleteProfile();

  /// Upload profile image (handled separately due to MultipartFile issues)
  // @POST('/auth/profile/image')
  // @MultiPart()
  // Future<Map<String, dynamic>> uploadProfileImage(
  //   @Part(name: "file") MultipartFile file,
  // );

  /// Delete profile image
  @DELETE('/auth/profile/image')
  Future<Map<String, dynamic>> deleteProfileImage();

  /// Get profile image info
  @GET('/auth/profile/image/info')
  Future<Map<String, dynamic>> getProfileImageInfo();

  /// Verify JWT token
  @GET('/auth/verify-token')
  Future<Map<String, dynamic>> verifyToken();

  /// Get current user info (alias for getProfile)
  @GET('/auth/me')
  Future<Map<String, dynamic>> getCurrentUserInfo();

  /// Deactivate user account
  @POST('/auth/deactivate')
  Future<Map<String, dynamic>> deactivateAccount();

  /// Get user type
  @GET('/auth/user-type')
  Future<Map<String, dynamic>> getUserType();

  /// Get user permissions
  @GET('/auth/permissions')
  Future<Map<String, dynamic>> getUserPermissions();

  /// Get profile completeness status
  @GET('/auth/profile/completeness')
  Future<Map<String, dynamic>> getProfileCompleteness();

  /// Get profile validation info
  @GET('/auth/profile/validation')
  Future<Map<String, dynamic>> getProfileValidation();

  /// Get token info (debug endpoint)
  @GET('/auth/debug/token-info')
  Future<Map<String, dynamic>> getTokenInfo();

  /// Health check
  @GET('/auth/health')
  Future<Map<String, dynamic>> healthCheck();
}
