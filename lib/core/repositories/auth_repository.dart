import 'package:dio/dio.dart';

import '../api/auth_api.dart';
import '../exceptions/api_exception.dart';
import '../models/api_response.dart';
import '../services/token_service.dart';
import 'base_repository.dart';

/// Repository for authentication-related operations
class AuthRepository extends BaseRepository {
  AuthRepository({
    required AuthApi authApi,
    required TokenService tokenService,
    required Dio dio,
  }) : _authApi = authApi,
       _tokenService = tokenService,
       _dio = dio;

  final AuthApi _authApi;
  final TokenService _tokenService;
  final Dio _dio;

  /// Register a new user
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
  }) async {
    logOperation(
      'register',
      params: {
        'email': email,
        'userType': userType,
        'firstName': firstName,
        'lastName': lastName,
      },
    );

    return handleApiCall(
      _authApi.register({
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'user_type': userType,
        if (phoneNumber != null) 'phone_number': phoneNumber,
      }),
      errorContext: 'register',
    );
  }

  /// Login user
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    logOperation('login', params: {'email': email, 'rememberMe': rememberMe});

    return handleApiCall(
      _authApi.login({'email': email, 'password': password}),
      errorContext: 'login',
    );
  }

  /// Logout user
  Future<Map<String, dynamic>> logout() async {
    logOperation('logout');

    final Map<String, dynamic> response = await handleApiCall(
      _authApi.logout(),
      errorContext: 'logout',
    );

    // Clear tokens regardless of API response
    await _tokenService.clearTokens();

    return response;
  }

  /// Get current user profile
  Future<Map<String, dynamic>> getProfile() async {
    logOperation('getProfile');

    return handleApiCall(_authApi.getProfile(), errorContext: 'getProfile');
  }

  /// Update user profile
  Future<Map<String, dynamic>> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
  }) async {
    logOperation('updateProfile');

    final Map<String, dynamic> updateData = <String, dynamic>{};

    if (firstName != null) updateData['first_name'] = firstName;
    if (lastName != null) updateData['last_name'] = lastName;
    if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
    if (dateOfBirth != null) updateData['date_of_birth'] = dateOfBirth;
    if (address != null) updateData['address'] = address;
    if (city != null) updateData['city'] = city;
    if (country != null) updateData['country'] = country;

    return handleApiCall(
      _authApi.updateProfile(updateData),
      errorContext: 'updateProfile',
    );
  }

  /// Get complete user profile with type-specific data
  Future<Map<String, dynamic>> getCompleteProfile() async {
    logOperation('getCompleteProfile');

    return handleApiCall(
      _authApi.getCompleteProfile(),
      errorContext: 'getCompleteProfile',
    );
  }

  /// Upload profile image (using direct Dio call due to Retrofit MultipartFile issues)
  Future<ApiResponse<Map<String, dynamic>>> uploadProfileImage({
    required String filePath,
  }) async {
    logOperation('uploadProfileImage', params: {'filePath': filePath});

    try {
      final MultipartFile file = await MultipartFile.fromFile(filePath);
      final FormData formData = FormData.fromMap({'file': file});

      // Use direct Dio call instead of Retrofit
      final Response response = await _dio.post(
        '/auth/profile/image',
        data: formData,
      );

      return ApiResponse.success(response.data as Map<String, dynamic>);
    } catch (e) {
      logError('uploadProfileImage', e);
      return ApiResponse.error(
        ApiException(
          message: 'Failed to upload profile image',
          context: 'uploadProfileImage',
          originalError: e,
        ),
      );
    }
  }

  /// Delete profile image
  Future<Map<String, dynamic>> deleteProfileImage() async {
    logOperation('deleteProfileImage');

    return handleApiCall(
      _authApi.deleteProfileImage(),
      errorContext: 'deleteProfileImage',
    );
  }

  /// Get profile image info
  Future<Map<String, dynamic>> getProfileImageInfo() async {
    logOperation('getProfileImageInfo');

    return handleApiCall(
      _authApi.getProfileImageInfo(),
      errorContext: 'getProfileImageInfo',
    );
  }

  /// Verify JWT token
  Future<Map<String, dynamic>> verifyToken() async {
    logOperation('verifyToken');

    return handleApiCall(_authApi.verifyToken(), errorContext: 'verifyToken');
  }

  /// Get current user info
  Future<Map<String, dynamic>> getCurrentUserInfo() async {
    logOperation('getCurrentUserInfo');

    return handleApiCall(
      _authApi.getCurrentUserInfo(),
      errorContext: 'getCurrentUserInfo',
    );
  }

  /// Deactivate user account
  Future<Map<String, dynamic>> deactivateAccount() async {
    logOperation('deactivateAccount');

    final Map<String, dynamic> response = await handleApiCall(
      _authApi.deactivateAccount(),
      errorContext: 'deactivateAccount',
    );

    // Clear tokens after successful deactivation
    await _tokenService.clearTokens();

    return response;
  }

  /// Get user type
  Future<Map<String, dynamic>> getUserType() async {
    logOperation('getUserType');

    return handleApiCall(_authApi.getUserType(), errorContext: 'getUserType');
  }

  /// Get user permissions
  Future<Map<String, dynamic>> getUserPermissions() async {
    logOperation('getUserPermissions');

    return handleApiCall(
      _authApi.getUserPermissions(),
      errorContext: 'getUserPermissions',
    );
  }

  /// Get profile completeness status
  Future<ApiResponse<Map<String, dynamic>>> getProfileCompleteness() async {
    logOperation('getProfileCompleteness');

    final Map<String, dynamic> response = await handleApiCall(
      _authApi.getProfileCompleteness(),
      errorContext: 'getProfileCompleteness',
    );
    return ApiResponse.success(response);
  }

  /// Get profile validation info
  Future<ApiResponse<Map<String, dynamic>>> getProfileValidation() async {
    logOperation('getProfileValidation');

    final Map<String, dynamic> response = await handleApiCall(
      _authApi.getProfileValidation(),
      errorContext: 'getProfileValidation',
    );
    return ApiResponse.success(response);
  }

  /// Health check
  Future<ApiResponse<Map<String, dynamic>>> healthCheck() async {
    logOperation('healthCheck');

    final Map<String, dynamic> response = await handleApiCall(
      _authApi.healthCheck(),
      errorContext: 'healthCheck',
    );
    return ApiResponse.success(response);
  }
}
