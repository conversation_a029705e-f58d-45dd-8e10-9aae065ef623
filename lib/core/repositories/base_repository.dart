import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../exceptions/api_exception.dart';
import '../models/api_response.dart';

/// Base repository class that provides common functionality for all repositories
/// Handles error processing, response parsing, and common API operations
abstract class BaseRepository {
  BaseRepository() : _logger = Logger();

  final Logger _logger;

  /// Handle API response and convert to typed result
  Future<T> handleApiCall<T>(
    Future<Response> apiCall, {
    T Function(dynamic data)? parser,
    String? errorContext,
  }) async {
    try {
      final Response response = await apiCall;

      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! < 300) {
        if (parser != null) {
          return parser(response.data);
        } else {
          return response.data as T;
        }
      } else {
        throw ApiException(
          message: 'Unexpected status code: ${response.statusCode}',
          statusCode: response.statusCode,
          context: errorContext,
        );
      }
    } on DioException catch (e) {
      _logger.e('API call failed: ${errorContext ?? 'Unknown'} - ${e.message}');
      throw _handleDioException(e, errorContext);
    } catch (e) {
      _logger.e(
        'Unexpected error in API call: ${errorContext ?? 'Unknown'} - $e',
      );
      throw ApiException(
        message: 'Unexpected error occurred',
        context: errorContext,
        originalError: e,
      );
    }
  }

  /// Handle direct API response (for APIs that return data directly)
  Future<T> handleDirectApiCall<T>(
    Future<T> apiCall, {
    T Function(T data)? parser,
    String? errorContext,
  }) async {
    try {
      final T response = await apiCall;

      if (parser != null) {
        return parser(response);
      } else {
        return response;
      }
    } on DioException catch (e) {
      _logger.e('API call failed: ${errorContext ?? 'Unknown'} - ${e.message}');
      throw _handleDioException(e, errorContext);
    } catch (e) {
      _logger.e(
        'Unexpected error in API call: ${errorContext ?? 'Unknown'} - $e',
      );
      throw ApiException(
        message: 'Unexpected error occurred',
        context: errorContext,
        originalError: e,
      );
    }
  }

  /// Handle API response with ApiResponse wrapper
  Future<ApiResponse<T>> handleApiResponse<T>(
    Future<Response> apiCall, {
    T Function(dynamic data)? parser,
    String? errorContext,
  }) async {
    try {
      final T result = await handleApiCall<T>(
        apiCall,
        parser: parser,
        errorContext: errorContext,
      );

      return ApiResponse.success(result);
    } on ApiException catch (e) {
      return ApiResponse.error(e);
    } catch (e) {
      final ApiException apiException = ApiException(
        message: 'Unexpected error occurred',
        context: errorContext,
        originalError: e,
      );
      return ApiResponse.error(apiException);
    }
  }

  /// Convert DioException to ApiException
  ApiException _handleDioException(DioException dioException, String? context) {
    switch (dioException.type) {
      case DioExceptionType.connectionTimeout:
        return ApiException(
          message: 'Connection timeout. Please check your internet connection.',
          type: ApiExceptionType.network,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.sendTimeout:
        return ApiException(
          message: 'Request timeout. Please try again.',
          type: ApiExceptionType.network,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.receiveTimeout:
        return ApiException(
          message: 'Response timeout. Please try again.',
          type: ApiExceptionType.network,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.badResponse:
        return _handleBadResponse(dioException, context);

      case DioExceptionType.cancel:
        return ApiException(
          message: 'Request was cancelled.',
          type: ApiExceptionType.cancelled,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.connectionError:
        return ApiException(
          message: 'No internet connection. Please check your network.',
          type: ApiExceptionType.network,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.badCertificate:
        return ApiException(
          message: 'Security certificate error.',
          type: ApiExceptionType.security,
          context: context,
          originalError: dioException,
        );

      case DioExceptionType.unknown:
      default:
        return ApiException(
          message: 'An unexpected error occurred. Please try again.',
          type: ApiExceptionType.unknown,
          context: context,
          originalError: dioException,
        );
    }
  }

  /// Handle bad response (4xx, 5xx status codes)
  ApiException _handleBadResponse(DioException dioException, String? context) {
    final int? statusCode = dioException.response?.statusCode;
    final dynamic responseData = dioException.response?.data;

    String message = 'An error occurred. Please try again.';
    ApiExceptionType type = ApiExceptionType.server;

    // Try to extract error message from response
    if (responseData is Map<String, dynamic>) {
      message =
          responseData['message'] ??
          responseData['error'] ??
          responseData['detail'] ??
          message;
    }

    switch (statusCode) {
      case 400:
        type = ApiExceptionType.validation;
        message = message.isEmpty ? 'Invalid request data.' : message;
        break;

      case 401:
        type = ApiExceptionType.authentication;
        message = 'Authentication failed. Please log in again.';
        break;

      case 403:
        type = ApiExceptionType.authorization;
        message = 'You do not have permission to perform this action.';
        break;

      case 404:
        type = ApiExceptionType.notFound;
        message = 'The requested resource was not found.';
        break;

      case 422:
        type = ApiExceptionType.validation;
        message = message.isEmpty ? 'Validation failed.' : message;
        break;

      case 429:
        type = ApiExceptionType.rateLimit;
        message = 'Too many requests. Please try again later.';
        break;

      case 500:
      case 502:
      case 503:
      case 504:
        type = ApiExceptionType.server;
        message = 'Server error. Please try again later.';
        break;

      default:
        type = ApiExceptionType.unknown;
        break;
    }

    return ApiException(
      message: message,
      type: type,
      statusCode: statusCode,
      context: context,
      originalError: dioException,
    );
  }

  /// Log repository operation
  void logOperation(String operation, {Map<String, dynamic>? params}) {
    _logger.d(
      'Repository operation: $operation${params != null ? ' with params: $params' : ''}',
    );
  }

  /// Log repository error
  void logError(String operation, dynamic error) {
    _logger.e('Repository error in $operation: $error');
  }
}
