import 'package:flutter/material.dart';

import '../../app/theme/index.dart';
import '../../core/exceptions/app_exception.dart';
import 'custom_button.dart';

/// Error display widget with retry functionality
class ErrorWidget extends StatelessWidget {
  const ErrorWidget({
    super.key,
    required this.exception,
    this.onRetry,
    this.showDetails = false,
  });

  final AppException exception;
  final VoidCallback? onRetry;
  final bool showDetails;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Error icon
          Icon(_getErrorIcon(), size: 64, color: _getErrorColor()),

          const SizedBox(height: AppSpacing.lg),

          // Error title
          Text(
            _getErrorTitle(),
            style: AppTextStyles.heading3.copyWith(color: _getErrorColor()),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppSpacing.md),

          // Error message
          Text(
            exception.userFriendlyMessage,
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppSpacing.sm),

          // Suggested action
          Text(
            exception.suggestedAction,
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppSpacing.xl),

          // Action buttons
          _buildActionButtons(context),

          // Error details (if enabled)
          if (showDetails) ...[
            const SizedBox(height: AppSpacing.lg),
            _buildErrorDetails(),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // Retry button (if recoverable and callback provided)
        if (exception.isRecoverable && onRetry != null)
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: 'Try Again',
              onPressed: onRetry,
              icon: Icons.refresh,
            ),
          ),

        // Additional actions based on error type
        if (exception.isRecoverable && onRetry != null)
          const SizedBox(height: AppSpacing.md),

        _buildSpecificActions(context),
      ],
    );
  }

  Widget _buildSpecificActions(BuildContext context) {
    return exception.when(
      network: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Check Connection',
            onPressed: () => _openNetworkSettings(context),
            buttonType: ButtonType.secondary,
            icon: Icons.wifi,
          ),

      authentication: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Login Again',
            onPressed: () => _navigateToLogin(context),
            buttonType: ButtonType.secondary,
            icon: Icons.login,
          ),

      authorization: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Contact Support',
            onPressed: () => _contactSupport(context),
            buttonType: ButtonType.secondary,
            icon: Icons.support_agent,
          ),

      permission: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Open Settings',
            onPressed: () => _openAppSettings(context),
            buttonType: ButtonType.secondary,
            icon: Icons.settings,
          ),

      location: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Enable Location',
            onPressed: () => _openLocationSettings(context),
            buttonType: ButtonType.secondary,
            icon: Icons.location_on,
          ),

      validation:
          (
            message,
            code,
            userMessage,
            fieldErrors,
            originalError,
            stackTrace,
          ) => const SizedBox.shrink(),

      server:
          (message, code, userMessage, statusCode, originalError, stackTrace) =>
              CustomButton(
                text: 'Report Issue',
                onPressed: () => _reportIssue(context),
                buttonType: ButtonType.secondary,
                icon: Icons.bug_report,
              ),

      timeout: (message, code, userMessage, originalError, stackTrace) =>
          const SizedBox.shrink(),

      unknown: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Contact Support',
            onPressed: () => _contactSupport(context),
            buttonType: ButtonType.secondary,
            icon: Icons.support_agent,
          ),

      storage: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Clear Cache',
            onPressed: () => _clearCache(context),
            buttonType: ButtonType.secondary,
            icon: Icons.clear_all,
          ),

      payment: (message, code, userMessage, originalError, stackTrace) =>
          CustomButton(
            text: 'Update Payment',
            onPressed: () => _updatePayment(context),
            buttonType: ButtonType.secondary,
            icon: Icons.payment,
          ),
    );
  }

  Widget _buildErrorDetails() {
    return ExpansionTile(
      title: Text('Error Details', style: AppTextStyles.buttonMedium),
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
            border: Border.all(color: AppColors.border),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (exception.errorCode != null) ...[
                Text(
                  'Error Code: ${exception.errorCode}',
                  style: AppTextStyles.caption.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
              ],

              Text('Technical Details:', style: AppTextStyles.buttonSmall),

              const SizedBox(height: AppSpacing.xs),

              Text(
                exception.when(
                  network:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  authentication:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  authorization:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  validation:
                      (
                        message,
                        code,
                        userMessage,
                        fieldErrors,
                        originalError,
                        stackTrace,
                      ) => message,
                  server:
                      (
                        message,
                        code,
                        userMessage,
                        statusCode,
                        originalError,
                        stackTrace,
                      ) => message,
                  timeout:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  unknown:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  storage:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  permission:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  location:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                  payment:
                      (message, code, userMessage, originalError, stackTrace) =>
                          message,
                ),
                style: AppTextStyles.caption.copyWith(
                  fontFamily: 'monospace',
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getErrorIcon() {
    return exception.when(
      network: (message, code, userMessage, originalError, stackTrace) =>
          Icons.wifi_off,
      authentication: (message, code, userMessage, originalError, stackTrace) =>
          Icons.lock_outline,
      authorization: (message, code, userMessage, originalError, stackTrace) =>
          Icons.block,
      validation:
          (
            message,
            code,
            userMessage,
            fieldErrors,
            originalError,
            stackTrace,
          ) => Icons.error_outline,
      server:
          (message, code, userMessage, statusCode, originalError, stackTrace) =>
              Icons.dns,
      timeout: (message, code, userMessage, originalError, stackTrace) =>
          Icons.timer_off,
      unknown: (message, code, userMessage, originalError, stackTrace) =>
          Icons.help_outline,
      storage: (message, code, userMessage, originalError, stackTrace) =>
          Icons.storage,
      permission: (message, code, userMessage, originalError, stackTrace) =>
          Icons.security,
      location: (message, code, userMessage, originalError, stackTrace) =>
          Icons.location_off,
      payment: (message, code, userMessage, originalError, stackTrace) =>
          Icons.payment,
    );
  }

  Color _getErrorColor() {
    return exception.when(
      network: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.orange,
      authentication: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.error,
      authorization: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.error,
      validation:
          (
            message,
            code,
            userMessage,
            fieldErrors,
            originalError,
            stackTrace,
          ) => AppColors.warning,
      server:
          (message, code, userMessage, statusCode, originalError, stackTrace) =>
              AppColors.error,
      timeout: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.orange,
      unknown: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.mediumGray,
      storage: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.orange,
      permission: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.warning,
      location: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.blue,
      payment: (message, code, userMessage, originalError, stackTrace) =>
          AppColors.error,
    );
  }

  String _getErrorTitle() {
    return exception.when(
      network: (message, code, userMessage, originalError, stackTrace) =>
          'Connection Problem',
      authentication: (message, code, userMessage, originalError, stackTrace) =>
          'Authentication Failed',
      authorization: (message, code, userMessage, originalError, stackTrace) =>
          'Access Denied',
      validation:
          (
            message,
            code,
            userMessage,
            fieldErrors,
            originalError,
            stackTrace,
          ) => 'Invalid Input',
      server:
          (message, code, userMessage, statusCode, originalError, stackTrace) =>
              'Server Error',
      timeout: (message, code, userMessage, originalError, stackTrace) =>
          'Request Timeout',
      unknown: (message, code, userMessage, originalError, stackTrace) =>
          'Unexpected Error',
      storage: (message, code, userMessage, originalError, stackTrace) =>
          'Storage Error',
      permission: (message, code, userMessage, originalError, stackTrace) =>
          'Permission Required',
      location: (message, code, userMessage, originalError, stackTrace) =>
          'Location Required',
      payment: (message, code, userMessage, originalError, stackTrace) =>
          'Payment Error',
    );
  }

  // Action methods (placeholder implementations)
  void _openNetworkSettings(BuildContext context) {
    // TODO: Open network settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening network settings...')),
    );
  }

  void _navigateToLogin(BuildContext context) {
    // TODO: Navigate to login screen
    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);
  }

  void _contactSupport(BuildContext context) {
    // TODO: Open support contact
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening support contact...')));
  }

  void _openAppSettings(BuildContext context) {
    // TODO: Open app settings
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening app settings...')));
  }

  void _openLocationSettings(BuildContext context) {
    // TODO: Open location settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening location settings...')),
    );
  }

  void _reportIssue(BuildContext context) {
    // TODO: Open issue reporting
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Opening issue reporting...')));
  }

  void _clearCache(BuildContext context) {
    // TODO: Clear app cache
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Clearing cache...')));
  }

  void _updatePayment(BuildContext context) {
    // TODO: Navigate to payment settings
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening payment settings...')),
    );
  }
}

/// Compact error widget for inline display
class CompactErrorWidget extends StatelessWidget {
  const CompactErrorWidget({super.key, required this.exception, this.onRetry});

  final AppException exception;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: AppSpacing.iconMd,
          ),

          const SizedBox(width: AppSpacing.md),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exception.userFriendlyMessage,
                  style: AppTextStyles.buttonSmall.copyWith(
                    color: AppColors.error,
                  ),
                ),

                if (exception.isRecoverable) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    exception.suggestedAction,
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),

          if (exception.isRecoverable && onRetry != null) ...[
            const SizedBox(width: AppSpacing.md),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: onRetry,
              color: AppColors.error,
              iconSize: AppSpacing.iconSm,
            ),
          ],
        ],
      ),
    );
  }
}

/// Empty state widget
class EmptyStateWidget extends StatelessWidget {
  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.icon = Icons.inbox_outlined,
    this.actionText,
    this.onAction,
  });

  final String title;
  final String message;
  final IconData icon;
  final String? actionText;
  final VoidCallback? onAction;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: AppColors.onSurfaceVariant),

          const SizedBox(height: AppSpacing.lg),

          Text(
            title,
            style: AppTextStyles.heading3.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppSpacing.md),

          Text(
            message,
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),

          if (actionText != null && onAction != null) ...[
            const SizedBox(height: AppSpacing.xl),
            CustomButton(
              text: actionText!,
              onPressed: onAction,
              buttonType: ButtonType.secondary,
            ),
          ],
        ],
      ),
    );
  }
}
