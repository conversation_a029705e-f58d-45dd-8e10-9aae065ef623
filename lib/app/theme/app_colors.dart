import 'package:flutter/material.dart';

/// Application color palette based on the design system
/// Colors are defined according to the design.json specifications
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary Colors
  static const Color primary = Color(0xFF00B5A5); // Teal
  static const Color primaryDark = Color(0xFF008B7A);
  static const Color primaryLight = Color(0xFF4DD0C7);

  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color mediumGray = Color(0xFFE0E0E0);
  static const Color darkGray = Color(0xFF666666);
  static const Color charcoal = Color(0xFF2C2C2C);
  static const Color black = Color(0xFF000000);

  // Accent Colors
  static const Color blue = Color(0xFF007AFF);
  static const Color yellow = Color(0xFFFFD700);
  static const Color green = Color(0xFF34C759);
  static const Color orange = Color(0xFFFF9500);

  // Status Colors
  static const Color success = Color(0xFF34C759);
  static const Color warning = Color(0xFFFF9500);
  static const Color error = Color(0xFFFF3B30);
  static const Color info = Color(0xFF007AFF);

  // Background Colors
  static const Color background = white;
  static const Color surface = white;
  static const Color surfaceVariant = lightGray;

  // Text Colors
  static const Color onPrimary = white;
  static const Color onBackground = black;
  static const Color onSurface = black;
  static const Color onSurfaceVariant = darkGray;

  // Border Colors
  static const Color border = mediumGray;
  static const Color borderFocus = primary;

  // Shadow Colors
  static const Color shadow = Color(0x1A000000); // 10% black
  static const Color shadowLight = Color(0x0D000000); // 5% black

  /// Get a color with specified opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// Get primary color with opacity
  static Color primaryWithOpacity(double opacity) {
    return primary.withValues(alpha: opacity);
  }

  /// Get black color with opacity
  static Color blackWithOpacity(double opacity) {
    return black.withValues(alpha: opacity);
  }

  /// Get white color with opacity
  static Color whiteWithOpacity(double opacity) {
    return white.withValues(alpha: opacity);
  }

  /// Color scheme for Material 3
  static ColorScheme get colorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.light,
    primary: primary,
    onPrimary: onPrimary,
    secondary: blue,
    onSecondary: white,
    error: error,
    onError: white,
    // background: background, // Deprecated - use surface instead
    // onBackground: onBackground, // Deprecated - use onSurface instead
    surface: surface,
    onSurface: onSurface,
    // surfaceVariant: surfaceVariant, // Deprecated - use surfaceContainerHighest instead
    surfaceContainerHighest: surfaceVariant,
    onSurfaceVariant: onSurfaceVariant,
  );

  /// Dark color scheme for Material 3
  static ColorScheme get darkColorScheme => ColorScheme.fromSeed(
    seedColor: primary,
    brightness: Brightness.dark,
    primary: primaryLight,
    onPrimary: black,
    secondary: blue,
    onSecondary: black,
    error: error,
    onError: black,
    // background: charcoal, // Deprecated - use surface instead
    // onBackground: white, // Deprecated - use onSurface instead
    surface: Color(0xFF1E1E1E),
    onSurface: white,
    // surfaceVariant: Color(0xFF2A2A2A), // Deprecated - use surfaceContainerHighest instead
    surfaceContainerHighest: Color(0xFF2A2A2A),
    onSurfaceVariant: lightGray,
  );

  /// Status color map for easy access
  static const Map<String, Color> statusColors = {
    'success': success,
    'warning': warning,
    'error': error,
    'info': info,
  };

  /// Get status color by name
  static Color getStatusColor(String status) {
    return statusColors[status] ?? info;
  }

  /// Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [white, lightGray],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  /// Button colors
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = lightGray;
  static const Color buttonGhost = Colors.transparent;

  /// Input field colors
  static const Color inputBackground = lightGray;
  static const Color inputBorder = mediumGray;
  static const Color inputFocusBorder = primary;
  static const Color inputText = black;
  static const Color inputPlaceholder = darkGray;

  /// Card colors
  static const Color cardBackground = white;
  static const Color cardBorder = mediumGray;
  static const Color cardShadow = shadow;

  /// Navigation colors
  static const Color navBackground = white;
  static const Color navActive = blue;
  static const Color navInactive = darkGray;

  /// Map pin colors
  static const Color pickupPin = primary;
  static const Color destinationPin = blue;

  /// Rating colors
  static const Color ratingActive = yellow;
  static const Color ratingInactive = mediumGray;

  /// Badge colors
  static const Color badgeBackground = error;
  static const Color badgeText = white;

  /// Modal and overlay colors
  static const Color modalBackground = white;
  static const Color overlayBackground = Color(0x80000000); // 50% black

  /// Divider colors
  static const Color divider = mediumGray;
  static const Color dividerLight = Color(0xFFF0F0F0);

  /// Shimmer colors for loading states
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);

  /// Get color for user type
  static Color getUserTypeColor(String userType) {
    switch (userType.toLowerCase()) {
      case 'rider':
        return blue;
      case 'driver':
        return primary;
      case 'admin':
        return orange;
      default:
        return darkGray;
    }
  }

  /// Get contrasting text color for a given background color
  static Color getContrastingTextColor(Color backgroundColor) {
    // Calculate luminance
    final double luminance = backgroundColor.computeLuminance();

    // Return white for dark backgrounds, black for light backgrounds
    return luminance > 0.5 ? black : white;
  }

  /// Disabled colors
  static const Color disabled = Color(0xFFBDBDBD);
  static const Color onDisabled = Color(0xFF9E9E9E);

  /// Focus colors
  static const Color focus = primary;
  static const Color focusOutline = Color(0x33000000); // 20% black

  /// Hover colors (for web/desktop)
  static Color getHoverColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.8);
  }

  /// Pressed colors (for interactions)
  static Color getPressedColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.6);
  }
}
