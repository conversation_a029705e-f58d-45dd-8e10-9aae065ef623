/// Application spacing constants based on the design system
/// Provides consistent spacing values throughout the app
class AppSpacing {
  // Private constructor to prevent instantiation
  AppSpacing._();

  // Base spacing values (from design.json)
  static const double xs = 4.0;
  static const double sm = 8.0;
  static const double md = 16.0;
  static const double lg = 24.0;
  static const double xl = 32.0;
  static const double xxl = 48.0;
  static const double xxxl = 64.0;

  // Common spacing aliases
  static const double tiny = xs;
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;
  static const double extraLarge = xl;
  static const double huge = xxl;
  static const double massive = xxxl;

  // Layout-specific spacing
  static const double containerPadding = md; // 16px
  static const double sectionSpacing = lg; // 24px
  static const double cardPadding = md; // 16px
  static const double listItemPadding = md; // 16px

  // Component spacing
  static const double buttonPadding = md; // 16px
  static const double inputPadding = md; // 16px
  static const double iconPadding = sm; // 8px
  static const double chipPadding = sm; // 8px

  // Margin values
  static const double marginXs = xs; // 4px
  static const double marginSm = sm; // 8px
  static const double marginMd = md; // 16px
  static const double marginLg = lg; // 24px
  static const double marginXl = xl; // 32px

  // Padding values
  static const double paddingXs = xs; // 4px
  static const double paddingSm = sm; // 8px
  static const double paddingMd = md; // 16px
  static const double paddingLg = lg; // 24px
  static const double paddingXl = xl; // 32px

  // Gap values for Flex widgets
  static const double gapXs = xs; // 4px
  static const double gapSm = sm; // 8px
  static const double gapMd = md; // 16px
  static const double gapLg = lg; // 24px
  static const double gapXl = xl; // 32px

  // Border radius values
  static const double radiusNone = 0.0;
  static const double radiusSm = 4.0;
  static const double radiusMd = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radiusXxl = 24.0;
  static const double radiusFull = 50.0; // For circular elements

  // Component-specific border radius
  static const double buttonRadius = radiusLg; // 12px
  static const double inputRadius = radiusLg; // 12px
  static const double cardRadius = radiusLg; // 12px
  static const double modalRadius = radiusXl; // 16px
  static const double bottomSheetRadius = radiusXl; // 16px
  static const double chipRadius = radiusFull; // Fully rounded

  // Layout dimensions
  static const double statusBarHeight = 44.0;
  static const double tabBarHeight = 83.0;
  static const double headerHeight = 56.0;
  static const double bottomNavHeight = 60.0;
  static const double fabSize = 56.0;

  // Component heights
  static const double buttonHeight = 48.0;
  static const double inputHeight = 48.0;
  static const double listItemHeight = 72.0;
  static const double appBarHeight = 56.0;
  static const double toolbarHeight = 56.0;

  // Icon sizes
  static const double iconXs = 16.0;
  static const double iconSm = 20.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;

  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 48.0;
  static const double avatarLg = 64.0;
  static const double avatarXl = 96.0;

  // Touch target sizes (minimum 44px for accessibility)
  static const double minTouchTarget = 44.0;
  static const double touchTargetSm = 44.0;
  static const double touchTargetMd = 48.0;
  static const double touchTargetLg = 56.0;

  // Elevation values
  static const double elevationNone = 0.0;
  static const double elevationSm = 1.0;
  static const double elevationMd = 4.0;
  static const double elevationLg = 8.0;
  static const double elevationXl = 16.0;

  // Animation durations (in milliseconds)
  static const int animationFast = 150;
  static const int animationNormal = 300;
  static const int animationSlow = 500;

  // Breakpoints for responsive design
  static const double mobileBreakpoint = 414.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;

  // Maximum widths
  static const double maxMobileWidth = 375.0;
  static const double maxContentWidth = 1200.0;

  // Grid spacing
  static const double gridSpacing = md; // 16px
  static const double gridMargin = md; // 16px

  // Form spacing
  static const double formFieldSpacing = md; // 16px
  static const double formSectionSpacing = lg; // 24px
  static const double formGroupSpacing = xl; // 32px

  // Card spacing
  static const double cardMargin = sm; // 8px
  static const double cardSpacing = md; // 16px between cards

  // List spacing
  static const double listSpacing = 0.0; // No spacing between list items
  static const double listSectionSpacing = lg; // 24px between sections

  // Modal and dialog spacing
  static const double modalPadding = lg; // 24px
  static const double dialogPadding = lg; // 24px
  static const double bottomSheetPadding = lg; // 24px

  // Snackbar and toast spacing
  static const double snackbarMargin = md; // 16px
  static const double toastMargin = md; // 16px

  // Map and location spacing
  static const double mapPadding = md; // 16px
  static const double pinSize = iconMd; // 24px

  // Rating and review spacing
  static const double ratingSpacing = xs; // 4px between stars
  static const double reviewSpacing = md; // 16px

  // Badge and chip spacing
  static const double badgeSize = 20.0;
  static const double chipHeight = 32.0;
  static const double chipSpacing = sm; // 8px

  // Search and filter spacing
  static const double searchBarHeight = inputHeight; // 48px
  static const double filterChipSpacing = sm; // 8px

  // Tab spacing
  static const double tabHeight = 48.0;
  static const double tabPadding = md; // 16px

  // Divider thickness
  static const double dividerThickness = 1.0;
  static const double thickDividerThickness = 2.0;

  // Border widths
  static const double borderThin = 1.0;
  static const double borderMedium = 2.0;
  static const double borderThick = 4.0;

  // Helper methods for responsive spacing
  static double responsive({
    required double mobile,
    double? tablet,
    double? desktop,
    required double screenWidth,
  }) {
    if (screenWidth >= desktopBreakpoint && desktop != null) {
      return desktop;
    } else if (screenWidth >= tabletBreakpoint && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// Get spacing value by name
  static double getSpacing(String name) {
    switch (name.toLowerCase()) {
      case 'xs':
      case 'tiny':
        return xs;
      case 'sm':
      case 'small':
        return sm;
      case 'md':
      case 'medium':
        return md;
      case 'lg':
      case 'large':
        return lg;
      case 'xl':
      case 'extralarge':
        return xl;
      case 'xxl':
      case 'huge':
        return xxl;
      case 'xxxl':
      case 'massive':
        return xxxl;
      default:
        return md; // Default to medium
    }
  }

  /// Get border radius by name
  static double getRadius(String name) {
    switch (name.toLowerCase()) {
      case 'none':
        return radiusNone;
      case 'sm':
      case 'small':
        return radiusSm;
      case 'md':
      case 'medium':
        return radiusMd;
      case 'lg':
      case 'large':
        return radiusLg;
      case 'xl':
      case 'extralarge':
        return radiusXl;
      case 'xxl':
      case 'huge':
        return radiusXxl;
      case 'full':
      case 'circular':
        return radiusFull;
      default:
        return radiusMd; // Default to medium
    }
  }
}
