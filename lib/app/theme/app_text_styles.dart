import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

/// Application text styles based on the design system
/// Uses Google Fonts for Roboto, Inter, and Open Sans with proper fallbacks
class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();

  // Font families (from design.json)
  static const String primaryFontFamily = 'Roboto';
  static const String secondaryFontFamily = 'Inter';
  static const String fallbackFontFamily = 'Open Sans';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semibold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;

  // Line heights
  static const double tightLineHeight = 1.2;
  static const double normalLineHeight = 1.4;
  static const double relaxedLineHeight = 1.6;

  /// Primary text styles using Roboto font
  static TextStyle get heading1 => GoogleFonts.roboto(
        fontSize: 32,
        fontWeight: bold,
        height: tightLineHeight,
        color: AppColors.onBackground,
        letterSpacing: -0.5,
      );

  static TextStyle get heading2 => GoogleFonts.roboto(
        fontSize: 24,
        fontWeight: semibold,
        height: tightLineHeight,
        color: AppColors.onBackground,
        letterSpacing: -0.25,
      );

  static TextStyle get heading3 => GoogleFonts.roboto(
        fontSize: 20,
        fontWeight: semibold,
        height: tightLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get heading4 => GoogleFonts.roboto(
        fontSize: 18,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get body1 => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get body2 => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get caption => GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  static TextStyle get overline => GoogleFonts.roboto(
        fontSize: 10,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
        letterSpacing: 1.5,
      );

  /// Secondary text styles using Inter font for special cases
  static TextStyle get headingSecondary => GoogleFonts.inter(
        fontSize: 24,
        fontWeight: semibold,
        height: tightLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get bodySecondary => GoogleFonts.inter(
        fontSize: 16,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onBackground,
      );

  /// Button text styles
  static TextStyle get buttonLarge => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: semibold,
        height: 1.0,
        color: AppColors.onPrimary,
      );

  static TextStyle get buttonMedium => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: 1.0,
        color: AppColors.onPrimary,
      );

  static TextStyle get buttonSmall => GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: medium,
        height: 1.0,
        color: AppColors.onPrimary,
      );

  /// Input field text styles
  static TextStyle get inputText => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.inputText,
      );

  static TextStyle get inputLabel => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  static TextStyle get inputPlaceholder => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.inputPlaceholder,
      );

  static TextStyle get inputError => GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.error,
      );

  /// Navigation text styles
  static TextStyle get navLabel => GoogleFonts.roboto(
        fontSize: 10,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.navInactive,
      );

  static TextStyle get navLabelActive => GoogleFonts.roboto(
        fontSize: 10,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.navActive,
      );

  /// Card text styles
  static TextStyle get cardTitle => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onSurface,
      );

  static TextStyle get cardSubtitle => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  /// List item text styles
  static TextStyle get listTitle => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onSurface,
      );

  static TextStyle get listSubtitle => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  /// Status text styles
  static TextStyle get statusSuccess => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.success,
      );

  static TextStyle get statusWarning => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.warning,
      );

  static TextStyle get statusError => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.error,
      );

  /// Price and currency text styles
  static TextStyle get priceMain => GoogleFonts.roboto(
        fontSize: 24,
        fontWeight: bold,
        height: tightLineHeight,
        color: AppColors.onBackground,
      );

  static TextStyle get priceSecondary => GoogleFonts.roboto(
        fontSize: 16,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  /// Time and distance text styles
  static TextStyle get timeDistance => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: regular,
        height: normalLineHeight,
        color: AppColors.onSurfaceVariant,
      );

  /// Badge text style
  static TextStyle get badge => GoogleFonts.roboto(
        fontSize: 12,
        fontWeight: bold,
        height: 1.0,
        color: AppColors.badgeText,
      );

  /// Link text style
  static TextStyle get link => GoogleFonts.roboto(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        color: AppColors.blue,
        decoration: TextDecoration.underline,
      );

  /// Helper methods to create custom text styles

  /// Create a text style with custom color
  static TextStyle withColor(TextStyle baseStyle, Color color) {
    return baseStyle.copyWith(color: color);
  }

  /// Create a text style with custom font size
  static TextStyle withFontSize(TextStyle baseStyle, double fontSize) {
    return baseStyle.copyWith(fontSize: fontSize);
  }

  /// Create a text style with custom font weight
  static TextStyle withFontWeight(TextStyle baseStyle, FontWeight fontWeight) {
    return baseStyle.copyWith(fontWeight: fontWeight);
  }

  /// Create a text style with custom line height
  static TextStyle withLineHeight(TextStyle baseStyle, double lineHeight) {
    return baseStyle.copyWith(height: lineHeight);
  }

  /// Create a disabled text style
  static TextStyle disabled(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.disabled);
  }

  /// Create a text style for dark theme
  static TextStyle forDarkTheme(TextStyle baseStyle) {
    return baseStyle.copyWith(color: AppColors.white);
  }

  /// Get text style for user type
  static TextStyle getUserTypeStyle(String userType) {
    return GoogleFonts.roboto(
      fontSize: 14,
      fontWeight: medium,
      height: normalLineHeight,
      color: AppColors.getUserTypeColor(userType),
    );
  }

  /// Text theme for Material 3
  static TextTheme get textTheme => TextTheme(
        displayLarge: heading1,
        displayMedium: heading2,
        displaySmall: heading3,
        headlineLarge: heading2,
        headlineMedium: heading3,
        headlineSmall: heading4,
        titleLarge: heading3,
        titleMedium: heading4,
        titleSmall: cardTitle,
        bodyLarge: body1,
        bodyMedium: body2,
        bodySmall: caption,
        labelLarge: buttonLarge,
        labelMedium: buttonMedium,
        labelSmall: buttonSmall,
      );
}
