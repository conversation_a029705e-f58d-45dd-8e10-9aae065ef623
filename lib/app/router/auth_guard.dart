import 'package:auto_route/auto_route.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/di/injection.dart';
import '../../core/services/token_service.dart';
import '../../features/authentication/controllers/auth_controller.dart';
import 'app_router.dart';

/// Authentication guard for protecting routes
class AuthGuard extends AutoRouteGuard {
  AuthGuard(this._ref);

  final Ref _ref;

  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) async {
    // Check if user is authenticated
    final authState = _ref.read(authControllerProvider);
    final tokenService = getIt<TokenService>();

    // Check both state and token validity
    final bool isAuthenticated =
        authState.isAuthenticated && await tokenService.hasValidAccessToken();

    if (isAuthenticated) {
      // User is authenticated, allow navigation
      resolver.next();
    } else {
      // User is not authenticated, redirect to login
      router.navigate(const LoginRoute());
    }
  }
}

/// Guest guard for routes that should only be accessible to non-authenticated users
class GuestGuard extends AutoRouteGuard {
  GuestGuard(this._ref);

  final Ref _ref;

  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) async {
    // Check if user is authenticated
    final authState = _ref.read(authControllerProvider);
    final tokenService = getIt<TokenService>();

    // Check both state and token validity
    final bool isAuthenticated =
        authState.isAuthenticated && await tokenService.hasValidAccessToken();

    if (!isAuthenticated) {
      // User is not authenticated, allow navigation to guest routes
      resolver.next();
    } else {
      // User is authenticated, redirect to main app
      // TODO: Redirect to home screen when implemented
      router.navigate(const LoginRoute());
    }
  }
}

/// Role-based guard for protecting routes based on user type
class RoleGuard extends AutoRouteGuard {
  RoleGuard(this._ref, this._requiredRole);

  final Ref _ref;
  final String _requiredRole;

  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) async {
    // Check if user is authenticated first
    final authState = _ref.read(authControllerProvider);
    final tokenService = getIt<TokenService>();

    final bool isAuthenticated =
        authState.isAuthenticated && await tokenService.hasValidAccessToken();

    if (!isAuthenticated) {
      // User is not authenticated, redirect to login
      router.navigate(const LoginRoute());
      return;
    }

    // Check user role
    final user = authState.user;
    if (user != null &&
        user.userType.toLowerCase() == _requiredRole.toLowerCase()) {
      // User has the required role, allow navigation
      resolver.next();
    } else {
      // User doesn't have the required role, redirect to appropriate screen
      // TODO: Redirect to appropriate screen based on user role
      router.navigate(const LoginRoute());
    }
  }
}

/// Factory methods for creating guards
class GuardFactory {
  static AuthGuard createAuthGuard(Ref ref) => AuthGuard(ref);
  static GuestGuard createGuestGuard(Ref ref) => GuestGuard(ref);
  static RoleGuard createRiderGuard(Ref ref) => RoleGuard(ref, 'rider');
  static RoleGuard createDriverGuard(Ref ref) => RoleGuard(ref, 'driver');
  static RoleGuard createAdminGuard(Ref ref) => RoleGuard(ref, 'admin');
}
