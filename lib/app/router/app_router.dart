import 'package:auto_route/auto_route.dart';

import '../screens/splash_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/main_app_wrapper.dart';
import '../../features/authentication/screens/login_screen.dart';
import '../../features/authentication/screens/register_screen.dart';

part 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen,Route|Wrapper,Route')
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    AutoRoute(page: SplashScreenRoute.page, path: '/splash', initial: true),
    AutoRoute(page: OnboardingScreenRoute.page, path: '/onboarding'),
    AutoRoute(page: LoginScreenRoute.page, path: '/login'),
    AutoRoute(page: RegisterScreenRoute.page, path: '/register'),
    AutoRoute(page: MainAppWrapperRoute.page, path: '/main'),
    AutoRoute(page: LoginScreenRoute.page, path: '*'),
  ];
}

// Route pages are now defined directly in their respective screen files
// with @RoutePage() annotation
