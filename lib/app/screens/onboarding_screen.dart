import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../core/di/injection.dart';
import '../../core/services/storage_service.dart';
import '../router/app_router.dart';
import '../theme/index.dart';
import '../../shared/widgets/custom_button.dart';

/// Onboarding screen to introduce the app to new users
@RoutePage()
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Welcome to Lucian Rides',
      description:
          'Your reliable ride-sharing service in St. Lucia. Get where you need to go safely and affordably.',
      icon: Icons.directions_car,
      color: AppColors.primary,
    ),
    OnboardingPage(
      title: 'Book Your Ride',
      description:
          'Easily request a ride with just a few taps. Track your driver in real-time and enjoy a smooth journey.',
      icon: Icons.smartphone,
      color: AppColors.blue,
    ),
    OnboardingPage(
      title: 'Drive and Earn',
      description:
          'Join our driver community and start earning. Flexible hours, fair rates, and reliable support.',
      icon: Icons.attach_money,
      color: AppColors.green,
    ),
    OnboardingPage(
      title: 'Safe and Secure',
      description:
          'Your safety is our priority. All drivers are verified and trips are tracked for your peace of mind.',
      icon: Icons.security,
      color: AppColors.orange,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: TextButton(
                  onPressed: _skipOnboarding,
                  child: Text(
                    'Skip',
                    style: AppTextStyles.buttonMedium.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            ),

            // Page view
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),

            // Page indicators
            _buildPageIndicators(),

            const SizedBox(height: AppSpacing.xl),

            // Navigation buttons
            _buildNavigationButtons(),

            const SizedBox(height: AppSpacing.lg),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppSpacing.radiusFull),
            ),
            child: Icon(page.icon, size: 60, color: page.color),
          ),

          const SizedBox(height: AppSpacing.xxxl),

          // Title
          Text(
            page.title,
            style: AppTextStyles.heading2,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppSpacing.lg),

          // Description
          Text(
            page.description,
            style: AppTextStyles.body1.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
          width: _currentPage == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColors.primary
                : AppColors.mediumGray,
            borderRadius: BorderRadius.circular(AppSpacing.radiusFull),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.containerPadding,
      ),
      child: Row(
        children: [
          // Previous button
          if (_currentPage > 0)
            Expanded(
              child: CustomButton(
                text: 'Previous',
                onPressed: _previousPage,
                buttonType: ButtonType.secondary,
              ),
            ),

          if (_currentPage > 0) const SizedBox(width: AppSpacing.md),

          // Next/Get Started button
          Expanded(
            flex: _currentPage == 0 ? 1 : 1,
            child: CustomButton(
              text: _currentPage == _pages.length - 1 ? 'Get Started' : 'Next',
              onPressed: _currentPage == _pages.length - 1
                  ? _completeOnboarding
                  : _nextPage,
            ),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  void _completeOnboarding() async {
    // Mark onboarding as completed
    final storageService = getIt<StorageService>();
    await storageService.setOnboardingCompleted(true);

    // Navigate to login screen
    if (mounted) {
      context.router.navigate(const LoginScreenRoute());
    }
  }
}

/// Onboarding page data model
class OnboardingPage {
  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  final String title;
  final String description;
  final IconData icon;
  final Color color;
}
