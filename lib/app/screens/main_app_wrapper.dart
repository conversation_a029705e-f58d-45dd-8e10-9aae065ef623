import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/authentication/controllers/auth_controller.dart';
import '../../features/authentication/models/user.dart';
import '../theme/index.dart';
import '../../shared/widgets/loading_overlay.dart';

/// Main app wrapper that handles role-based navigation
class MainAppWrapper extends ConsumerWidget {
  const MainAppWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authControllerProvider);

    return LoadingOverlay(
      isLoading: authState.isLoading,
      child: _buildContent(context, authState),
    );
  }

  Widget _buildContent(BuildContext context, AuthState authState) {
    if (!authState.isAuthenticated || authState.user == null) {
      return _buildUnauthenticatedView();
    }

    final User user = authState.user!;

    switch (user.userType) {
      case UserType.rider:
        return _buildRiderApp(context, user);
      case UserType.driver:
        return _buildDriverApp(context, user);
      case UserType.admin:
        return _buildAdminApp(context, user);
    }
  }

  Widget _buildUnauthenticatedView() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: AppColors.error),
            SizedBox(height: AppSpacing.lg),
            Text('Authentication Required', style: AppTextStyles.heading3),
            SizedBox(height: AppSpacing.md),
            Text('Please log in to continue', style: AppTextStyles.body1),
          ],
        ),
      ),
    );
  }

  Widget _buildRiderApp(BuildContext context, User user) {
    return RiderMainScreen(user: user);
  }

  Widget _buildDriverApp(BuildContext context, User user) {
    return DriverMainScreen(user: user);
  }

  Widget _buildAdminApp(BuildContext context, User user) {
    return AdminMainScreen(user: user);
  }
}

/// Rider main screen with bottom navigation
class RiderMainScreen extends StatefulWidget {
  const RiderMainScreen({super.key, required this.user});

  final User user;

  @override
  State<RiderMainScreen> createState() => _RiderMainScreenState();
}

class _RiderMainScreenState extends State<RiderMainScreen> {
  int _currentIndex = 0;

  final List<RiderTab> _tabs = [
    RiderTab(label: 'Home', icon: Icons.home_outlined, activeIcon: Icons.home),
    RiderTab(
      label: 'Rides',
      icon: Icons.directions_car_outlined,
      activeIcon: Icons.directions_car,
    ),
    RiderTab(
      label: 'Activity',
      icon: Icons.history_outlined,
      activeIcon: Icons.history,
    ),
    RiderTab(
      label: 'Profile',
      icon: Icons.person_outlined,
      activeIcon: Icons.person,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildTabContent(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildTabContent() {
    switch (_currentIndex) {
      case 0:
        return RiderHomeTab(user: widget.user);
      case 1:
        return RiderRidesTab(user: widget.user);
      case 2:
        return RiderActivityTab(user: widget.user);
      case 3:
        return RiderProfileTab(user: widget.user);
      default:
        return RiderHomeTab(user: widget.user);
    }
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.onSurfaceVariant,
      items: _tabs.map((tab) {
        final bool isSelected = _tabs.indexOf(tab) == _currentIndex;
        return BottomNavigationBarItem(
          icon: Icon(isSelected ? tab.activeIcon : tab.icon),
          label: tab.label,
        );
      }).toList(),
    );
  }
}

/// Driver main screen with bottom navigation
class DriverMainScreen extends StatefulWidget {
  const DriverMainScreen({super.key, required this.user});

  final User user;

  @override
  State<DriverMainScreen> createState() => _DriverMainScreenState();
}

class _DriverMainScreenState extends State<DriverMainScreen> {
  int _currentIndex = 0;

  final List<DriverTab> _tabs = [
    DriverTab(label: 'Home', icon: Icons.home_outlined, activeIcon: Icons.home),
    DriverTab(
      label: 'Earnings',
      icon: Icons.attach_money_outlined,
      activeIcon: Icons.attach_money,
    ),
    DriverTab(
      label: 'Activity',
      icon: Icons.history_outlined,
      activeIcon: Icons.history,
    ),
    DriverTab(
      label: 'Profile',
      icon: Icons.person_outlined,
      activeIcon: Icons.person,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildTabContent(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildTabContent() {
    switch (_currentIndex) {
      case 0:
        return DriverHomeTab(user: widget.user);
      case 1:
        return DriverEarningsTab(user: widget.user);
      case 2:
        return DriverActivityTab(user: widget.user);
      case 3:
        return DriverProfileTab(user: widget.user);
      default:
        return DriverHomeTab(user: widget.user);
    }
  }

  Widget _buildBottomNavigation() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.onSurfaceVariant,
      items: _tabs.map((tab) {
        final bool isSelected = _tabs.indexOf(tab) == _currentIndex;
        return BottomNavigationBarItem(
          icon: Icon(isSelected ? tab.activeIcon : tab.icon),
          label: tab.label,
        );
      }).toList(),
    );
  }
}

/// Admin main screen with drawer navigation
class AdminMainScreen extends StatelessWidget {
  const AdminMainScreen({super.key, required this.user});

  final User user;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
      ),
      drawer: _buildDrawer(context),
      body: const AdminDashboardTab(),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(color: AppColors.primary),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppColors.onPrimary,
                  child: Text(
                    user.firstName[0].toUpperCase(),
                    style: AppTextStyles.heading2.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
                const SizedBox(height: AppSpacing.md),
                Text(
                  '${user.firstName} ${user.lastName}',
                  style: AppTextStyles.buttonLarge.copyWith(
                    color: AppColors.onPrimary,
                  ),
                ),
                Text(
                  'Administrator',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          ListTile(
            leading: const Icon(Icons.dashboard),
            title: const Text('Dashboard'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.people),
            title: const Text('Users'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.directions_car),
            title: const Text('Drivers'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.receipt),
            title: const Text('Rides'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Analytics'),
            onTap: () => Navigator.pop(context),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('Settings'),
            onTap: () => Navigator.pop(context),
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Logout'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Implement logout
            },
          ),
        ],
      ),
    );
  }
}

/// Tab data models
class RiderTab {
  const RiderTab({
    required this.label,
    required this.icon,
    required this.activeIcon,
  });

  final String label;
  final IconData icon;
  final IconData activeIcon;
}

class DriverTab {
  const DriverTab({
    required this.label,
    required this.icon,
    required this.activeIcon,
  });

  final String label;
  final IconData icon;
  final IconData activeIcon;
}

/// Placeholder tab screens (to be implemented in future phases)
class RiderHomeTab extends StatelessWidget {
  const RiderHomeTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Rider Home - Coming Soon'));
  }
}

class RiderRidesTab extends StatelessWidget {
  const RiderRidesTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Rider Rides - Coming Soon'));
  }
}

class RiderActivityTab extends StatelessWidget {
  const RiderActivityTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Rider Activity - Coming Soon'));
  }
}

class RiderProfileTab extends StatelessWidget {
  const RiderProfileTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Rider Profile - Coming Soon'));
  }
}

class DriverHomeTab extends StatelessWidget {
  const DriverHomeTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Driver Home - Coming Soon'));
  }
}

class DriverEarningsTab extends StatelessWidget {
  const DriverEarningsTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Driver Earnings - Coming Soon'));
  }
}

class DriverActivityTab extends StatelessWidget {
  const DriverActivityTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Driver Activity - Coming Soon'));
  }
}

class DriverProfileTab extends StatelessWidget {
  const DriverProfileTab({super.key, required this.user});
  final User user;

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Driver Profile - Coming Soon'));
  }
}

class AdminDashboardTab extends StatelessWidget {
  const AdminDashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(child: Text('Admin Dashboard - Coming Soon'));
  }
}
