import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../core/di/injection.dart';
import '../../core/services/storage_service.dart';
import '../../features/authentication/controllers/auth_controller.dart';
import '../router/app_router.dart';
import '../theme/index.dart';

/// Splash screen that handles app initialization and authentication check
@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
      ),
    );

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize dependencies
      await initializeDependencies();

      // Wait for minimum splash duration
      await Future.delayed(const Duration(seconds: 2));

      // Check if onboarding is completed
      final storageService = getIt<StorageService>();
      final bool onboardingCompleted = storageService.isOnboardingCompleted();

      if (mounted) {
        if (!onboardingCompleted) {
          // Show onboarding
          context.router.navigate(const OnboardingScreenRoute());
        } else {
          // Check authentication status
          final authState = ref.read(authControllerProvider);

          if (authState.isAuthenticated) {
            // User is authenticated, navigate to main app
            context.router.navigate(const MainAppWrapperRoute());
          } else {
            // User is not authenticated, navigate to login
            context.router.navigate(const LoginScreenRoute());
          }
        }
      }
    } catch (e) {
      // Handle initialization error
      if (mounted) {
        _showErrorDialog(e.toString());
      }
    }
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Initialization Error'),
        content: Text('Failed to initialize the app: $error'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _initializeApp(); // Retry initialization
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App logo
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(
                          AppSpacing.radiusXl,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.blackWithOpacity(0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.directions_car,
                        color: AppColors.primary,
                        size: 60,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.xl),

                    // App name
                    Text(
                      'Lucian Rides',
                      style: AppTextStyles.heading1.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: AppSpacing.sm),

                    // App tagline
                    Text(
                      'Your ride, your way',
                      style: AppTextStyles.body1.copyWith(
                        color: AppColors.whiteWithOpacity(0.8),
                      ),
                    ),

                    const SizedBox(height: AppSpacing.xxxl),

                    // Loading indicator
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.white,
                        ),
                        strokeWidth: 3,
                        backgroundColor: AppColors.whiteWithOpacity(0.3),
                      ),
                    ),

                    const SizedBox(height: AppSpacing.lg),

                    // Loading text
                    Text(
                      'Loading...',
                      style: AppTextStyles.body2.copyWith(
                        color: AppColors.whiteWithOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Alternative minimal splash screen
class MinimalSplashScreen extends StatelessWidget {
  const MinimalSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppColors.primary,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.directions_car, color: AppColors.white, size: 80),
            SizedBox(height: AppSpacing.lg),
            Text(
              'Lucian Rides',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
