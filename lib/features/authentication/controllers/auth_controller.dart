import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/di/injection.dart';
import '../../../core/models/api_response.dart';
import '../models/user.dart';
import '../services/auth_service_impl.dart';

/// Authentication state
class AuthState {
  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  final User? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Authentication controller using Riverpod
class AuthController extends StateNotifier<AuthState> {
  AuthController(this._authService) : super(const AuthState()) {
    _checkAuthenticationStatus();
  }

  final AuthServiceImpl _authService;
  final Logger _logger = Logger();

  /// Check if user is already authenticated
  Future<void> _checkAuthenticationStatus() async {
    try {
      final bool isAuthenticated = await _authService.isAuthenticated();
      
      if (isAuthenticated) {
        // Try to get stored user data
        final User? storedUser = await _authService.getStoredUser();
        
        if (storedUser != null) {
          state = state.copyWith(
            user: storedUser,
            isAuthenticated: true,
          );
        } else {
          // If no stored user, fetch from API
          await getCurrentUser();
        }
      }
    } catch (e) {
      _logger.e('Error checking authentication status: $e');
    }
  }

  /// Login user
  Future<void> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<User> response = await _authService.login(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          user: response.data,
          isLoading: false,
          isAuthenticated: true,
        );
        _logger.i('User logged in successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Login failed',
        );
        _logger.e('Login failed: ${response.error?.message}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Login error: $e');
    }
  }

  /// Register new user
  Future<void> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<User> response = await _authService.register(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        userType: userType,
        phoneNumber: phoneNumber,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          user: response.data,
          isLoading: false,
          isAuthenticated: true,
        );
        _logger.i('User registered successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Registration failed',
        );
        _logger.e('Registration failed: ${response.error?.message}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Registration error: $e');
    }
  }

  /// Logout user
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    try {
      await _authService.logout();
      
      state = const AuthState(); // Reset to initial state
      _logger.i('User logged out successfully');
    } catch (e) {
      // Even if logout fails, clear local state
      state = const AuthState();
      _logger.e('Logout error: $e');
    }
  }

  /// Get current user profile
  Future<void> getCurrentUser() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<User> response = await _authService.getCurrentUser();

      if (response.isSuccess) {
        state = state.copyWith(
          user: response.data,
          isLoading: false,
          isAuthenticated: true,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Failed to get user profile',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Get current user error: $e');
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<User> response = await _authService.updateProfile(
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        dateOfBirth: dateOfBirth,
        address: address,
        city: city,
        country: country,
      );

      if (response.isSuccess) {
        state = state.copyWith(
          user: response.data,
          isLoading: false,
        );
        _logger.i('Profile updated successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Failed to update profile',
        );
        _logger.e('Profile update failed: ${response.error?.message}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Profile update error: $e');
    }
  }

  /// Upload profile image
  Future<void> uploadProfileImage(String filePath) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<String> response = await _authService.uploadProfileImage(
        filePath: filePath,
      );

      if (response.isSuccess) {
        // Refresh user profile to get updated image URL
        await getCurrentUser();
        _logger.i('Profile image uploaded successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Failed to upload image',
        );
        _logger.e('Image upload failed: ${response.error?.message}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Image upload error: $e');
    }
  }

  /// Delete profile image
  Future<void> deleteProfileImage() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final ApiResponse<void> response = await _authService.deleteProfileImage();

      if (response.isSuccess) {
        // Refresh user profile to get updated image URL
        await getCurrentUser();
        _logger.i('Profile image deleted successfully');
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.error?.userFriendlyMessage ?? 'Failed to delete image',
        );
        _logger.e('Image deletion failed: ${response.error?.message}');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred',
      );
      _logger.e('Image deletion error: $e');
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Check if remember me is enabled
  bool getRememberMe() {
    return _authService.getRememberMe();
  }

  /// Get current user ID
  Future<String?> getCurrentUserId() async {
    return await _authService.getCurrentUserId();
  }

  /// Get current user email
  Future<String?> getCurrentUserEmail() async {
    return await _authService.getCurrentUserEmail();
  }
}

/// Provider for AuthService
final authServiceProvider = Provider<AuthServiceImpl>((ref) {
  return AuthServiceImpl(
    authRepository: getIt(),
    tokenService: getIt(),
    storageService: getIt(),
  );
});

/// Provider for AuthController
final authControllerProvider = StateNotifierProvider<AuthController, AuthState>((ref) {
  return AuthController(ref.read(authServiceProvider));
});

/// Provider for checking if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authControllerProvider).isAuthenticated;
});

/// Provider for current user
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authControllerProvider).user;
});
