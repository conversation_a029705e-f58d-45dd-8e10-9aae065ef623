// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get userType => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get dateOfBirth => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get country => throw _privateConstructorUsedError;
  String? get profileImageUrl => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {String id,
      String email,
      String firstName,
      String lastName,
      String userType,
      String? phoneNumber,
      String? dateOfBirth,
      String? address,
      String? city,
      String? country,
      String? profileImageUrl,
      bool isActive,
      bool isVerified,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? userType = null,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? profileImageUrl = freezed,
    Object? isActive = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String firstName,
      String lastName,
      String userType,
      String? phoneNumber,
      String? dateOfBirth,
      String? address,
      String? city,
      String? country,
      String? profileImageUrl,
      bool isActive,
      bool isVerified,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? userType = null,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? country = freezed,
    Object? profileImageUrl = freezed,
    Object? isActive = null,
    Object? isVerified = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {required this.id,
      required this.email,
      required this.firstName,
      required this.lastName,
      required this.userType,
      this.phoneNumber,
      this.dateOfBirth,
      this.address,
      this.city,
      this.country,
      this.profileImageUrl,
      required this.isActive,
      required this.isVerified,
      required this.createdAt,
      required this.updatedAt});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String userType;
  @override
  final String? phoneNumber;
  @override
  final String? dateOfBirth;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? country;
  @override
  final String? profileImageUrl;
  @override
  final bool isActive;
  @override
  final bool isVerified;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'User(id: $id, email: $email, firstName: $firstName, lastName: $lastName, userType: $userType, phoneNumber: $phoneNumber, dateOfBirth: $dateOfBirth, address: $address, city: $city, country: $country, profileImageUrl: $profileImageUrl, isActive: $isActive, isVerified: $isVerified, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      email,
      firstName,
      lastName,
      userType,
      phoneNumber,
      dateOfBirth,
      address,
      city,
      country,
      profileImageUrl,
      isActive,
      isVerified,
      createdAt,
      updatedAt);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {required final String id,
      required final String email,
      required final String firstName,
      required final String lastName,
      required final String userType,
      final String? phoneNumber,
      final String? dateOfBirth,
      final String? address,
      final String? city,
      final String? country,
      final String? profileImageUrl,
      required final bool isActive,
      required final bool isVerified,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get userType;
  @override
  String? get phoneNumber;
  @override
  String? get dateOfBirth;
  @override
  String? get address;
  @override
  String? get city;
  @override
  String? get country;
  @override
  String? get profileImageUrl;
  @override
  bool get isActive;
  @override
  bool get isVerified;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserCreate _$UserCreateFromJson(Map<String, dynamic> json) {
  return _UserCreate.fromJson(json);
}

/// @nodoc
mixin _$UserCreate {
  String get email => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get userType => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;

  /// Serializes this UserCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCreateCopyWith<UserCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCreateCopyWith<$Res> {
  factory $UserCreateCopyWith(
          UserCreate value, $Res Function(UserCreate) then) =
      _$UserCreateCopyWithImpl<$Res, UserCreate>;
  @useResult
  $Res call(
      {String email,
      String password,
      String firstName,
      String lastName,
      String userType,
      String? phoneNumber});
}

/// @nodoc
class _$UserCreateCopyWithImpl<$Res, $Val extends UserCreate>
    implements $UserCreateCopyWith<$Res> {
  _$UserCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? userType = null,
    Object? phoneNumber = freezed,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserCreateImplCopyWith<$Res>
    implements $UserCreateCopyWith<$Res> {
  factory _$$UserCreateImplCopyWith(
          _$UserCreateImpl value, $Res Function(_$UserCreateImpl) then) =
      __$$UserCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String email,
      String password,
      String firstName,
      String lastName,
      String userType,
      String? phoneNumber});
}

/// @nodoc
class __$$UserCreateImplCopyWithImpl<$Res>
    extends _$UserCreateCopyWithImpl<$Res, _$UserCreateImpl>
    implements _$$UserCreateImplCopyWith<$Res> {
  __$$UserCreateImplCopyWithImpl(
      _$UserCreateImpl _value, $Res Function(_$UserCreateImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? userType = null,
    Object? phoneNumber = freezed,
  }) {
    return _then(_$UserCreateImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserCreateImpl implements _UserCreate {
  const _$UserCreateImpl(
      {required this.email,
      required this.password,
      required this.firstName,
      required this.lastName,
      required this.userType,
      this.phoneNumber});

  factory _$UserCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserCreateImplFromJson(json);

  @override
  final String email;
  @override
  final String password;
  @override
  final String firstName;
  @override
  final String lastName;
  @override
  final String userType;
  @override
  final String? phoneNumber;

  @override
  String toString() {
    return 'UserCreate(email: $email, password: $password, firstName: $firstName, lastName: $lastName, userType: $userType, phoneNumber: $phoneNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserCreateImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, email, password, firstName, lastName, userType, phoneNumber);

  /// Create a copy of UserCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserCreateImplCopyWith<_$UserCreateImpl> get copyWith =>
      __$$UserCreateImplCopyWithImpl<_$UserCreateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserCreateImplToJson(
      this,
    );
  }
}

abstract class _UserCreate implements UserCreate {
  const factory _UserCreate(
      {required final String email,
      required final String password,
      required final String firstName,
      required final String lastName,
      required final String userType,
      final String? phoneNumber}) = _$UserCreateImpl;

  factory _UserCreate.fromJson(Map<String, dynamic> json) =
      _$UserCreateImpl.fromJson;

  @override
  String get email;
  @override
  String get password;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get userType;
  @override
  String? get phoneNumber;

  /// Create a copy of UserCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserCreateImplCopyWith<_$UserCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserLogin _$UserLoginFromJson(Map<String, dynamic> json) {
  return _UserLogin.fromJson(json);
}

/// @nodoc
mixin _$UserLogin {
  String get email => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;

  /// Serializes this UserLogin to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserLogin
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserLoginCopyWith<UserLogin> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserLoginCopyWith<$Res> {
  factory $UserLoginCopyWith(UserLogin value, $Res Function(UserLogin) then) =
      _$UserLoginCopyWithImpl<$Res, UserLogin>;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class _$UserLoginCopyWithImpl<$Res, $Val extends UserLogin>
    implements $UserLoginCopyWith<$Res> {
  _$UserLoginCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserLogin
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(_value.copyWith(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserLoginImplCopyWith<$Res>
    implements $UserLoginCopyWith<$Res> {
  factory _$$UserLoginImplCopyWith(
          _$UserLoginImpl value, $Res Function(_$UserLoginImpl) then) =
      __$$UserLoginImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class __$$UserLoginImplCopyWithImpl<$Res>
    extends _$UserLoginCopyWithImpl<$Res, _$UserLoginImpl>
    implements _$$UserLoginImplCopyWith<$Res> {
  __$$UserLoginImplCopyWithImpl(
      _$UserLoginImpl _value, $Res Function(_$UserLoginImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserLogin
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(_$UserLoginImpl(
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserLoginImpl implements _UserLogin {
  const _$UserLoginImpl({required this.email, required this.password});

  factory _$UserLoginImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserLoginImplFromJson(json);

  @override
  final String email;
  @override
  final String password;

  @override
  String toString() {
    return 'UserLogin(email: $email, password: $password)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserLoginImpl &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  /// Create a copy of UserLogin
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserLoginImplCopyWith<_$UserLoginImpl> get copyWith =>
      __$$UserLoginImplCopyWithImpl<_$UserLoginImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserLoginImplToJson(
      this,
    );
  }
}

abstract class _UserLogin implements UserLogin {
  const factory _UserLogin(
      {required final String email,
      required final String password}) = _$UserLoginImpl;

  factory _UserLogin.fromJson(Map<String, dynamic> json) =
      _$UserLoginImpl.fromJson;

  @override
  String get email;
  @override
  String get password;

  /// Create a copy of UserLogin
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserLoginImplCopyWith<_$UserLoginImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserUpdate _$UserUpdateFromJson(Map<String, dynamic> json) {
  return _UserUpdate.fromJson(json);
}

/// @nodoc
mixin _$UserUpdate {
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get dateOfBirth => throw _privateConstructorUsedError;
  String? get address => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get country => throw _privateConstructorUsedError;

  /// Serializes this UserUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserUpdateCopyWith<UserUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserUpdateCopyWith<$Res> {
  factory $UserUpdateCopyWith(
          UserUpdate value, $Res Function(UserUpdate) then) =
      _$UserUpdateCopyWithImpl<$Res, UserUpdate>;
  @useResult
  $Res call(
      {String? firstName,
      String? lastName,
      String? phoneNumber,
      String? dateOfBirth,
      String? address,
      String? city,
      String? country});
}

/// @nodoc
class _$UserUpdateCopyWithImpl<$Res, $Val extends UserUpdate>
    implements $UserUpdateCopyWith<$Res> {
  _$UserUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? country = freezed,
  }) {
    return _then(_value.copyWith(
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserUpdateImplCopyWith<$Res>
    implements $UserUpdateCopyWith<$Res> {
  factory _$$UserUpdateImplCopyWith(
          _$UserUpdateImpl value, $Res Function(_$UserUpdateImpl) then) =
      __$$UserUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? firstName,
      String? lastName,
      String? phoneNumber,
      String? dateOfBirth,
      String? address,
      String? city,
      String? country});
}

/// @nodoc
class __$$UserUpdateImplCopyWithImpl<$Res>
    extends _$UserUpdateCopyWithImpl<$Res, _$UserUpdateImpl>
    implements _$$UserUpdateImplCopyWith<$Res> {
  __$$UserUpdateImplCopyWithImpl(
      _$UserUpdateImpl _value, $Res Function(_$UserUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? address = freezed,
    Object? city = freezed,
    Object? country = freezed,
  }) {
    return _then(_$UserUpdateImpl(
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      country: freezed == country
          ? _value.country
          : country // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserUpdateImpl implements _UserUpdate {
  const _$UserUpdateImpl(
      {this.firstName,
      this.lastName,
      this.phoneNumber,
      this.dateOfBirth,
      this.address,
      this.city,
      this.country});

  factory _$UserUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserUpdateImplFromJson(json);

  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? phoneNumber;
  @override
  final String? dateOfBirth;
  @override
  final String? address;
  @override
  final String? city;
  @override
  final String? country;

  @override
  String toString() {
    return 'UserUpdate(firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, dateOfBirth: $dateOfBirth, address: $address, city: $city, country: $country)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserUpdateImpl &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.country, country) || other.country == country));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, firstName, lastName, phoneNumber,
      dateOfBirth, address, city, country);

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserUpdateImplCopyWith<_$UserUpdateImpl> get copyWith =>
      __$$UserUpdateImplCopyWithImpl<_$UserUpdateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserUpdateImplToJson(
      this,
    );
  }
}

abstract class _UserUpdate implements UserUpdate {
  const factory _UserUpdate(
      {final String? firstName,
      final String? lastName,
      final String? phoneNumber,
      final String? dateOfBirth,
      final String? address,
      final String? city,
      final String? country}) = _$UserUpdateImpl;

  factory _UserUpdate.fromJson(Map<String, dynamic> json) =
      _$UserUpdateImpl.fromJson;

  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get phoneNumber;
  @override
  String? get dateOfBirth;
  @override
  String? get address;
  @override
  String? get city;
  @override
  String? get country;

  /// Create a copy of UserUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserUpdateImplCopyWith<_$UserUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TokenResponse _$TokenResponseFromJson(Map<String, dynamic> json) {
  return _TokenResponse.fromJson(json);
}

/// @nodoc
mixin _$TokenResponse {
  String get accessToken => throw _privateConstructorUsedError;
  String? get refreshToken => throw _privateConstructorUsedError;
  String get tokenType => throw _privateConstructorUsedError;
  int get expiresIn => throw _privateConstructorUsedError;
  User get user => throw _privateConstructorUsedError;

  /// Serializes this TokenResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TokenResponseCopyWith<TokenResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TokenResponseCopyWith<$Res> {
  factory $TokenResponseCopyWith(
          TokenResponse value, $Res Function(TokenResponse) then) =
      _$TokenResponseCopyWithImpl<$Res, TokenResponse>;
  @useResult
  $Res call(
      {String accessToken,
      String? refreshToken,
      String tokenType,
      int expiresIn,
      User user});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class _$TokenResponseCopyWithImpl<$Res, $Val extends TokenResponse>
    implements $TokenResponseCopyWith<$Res> {
  _$TokenResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = freezed,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ) as $Val);
  }

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$TokenResponseImplCopyWith<$Res>
    implements $TokenResponseCopyWith<$Res> {
  factory _$$TokenResponseImplCopyWith(
          _$TokenResponseImpl value, $Res Function(_$TokenResponseImpl) then) =
      __$$TokenResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accessToken,
      String? refreshToken,
      String tokenType,
      int expiresIn,
      User user});

  @override
  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$TokenResponseImplCopyWithImpl<$Res>
    extends _$TokenResponseCopyWithImpl<$Res, _$TokenResponseImpl>
    implements _$$TokenResponseImplCopyWith<$Res> {
  __$$TokenResponseImplCopyWithImpl(
      _$TokenResponseImpl _value, $Res Function(_$TokenResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = freezed,
    Object? tokenType = null,
    Object? expiresIn = null,
    Object? user = null,
  }) {
    return _then(_$TokenResponseImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: freezed == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String?,
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: null == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TokenResponseImpl implements _TokenResponse {
  const _$TokenResponseImpl(
      {required this.accessToken,
      this.refreshToken,
      required this.tokenType,
      required this.expiresIn,
      required this.user});

  factory _$TokenResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$TokenResponseImplFromJson(json);

  @override
  final String accessToken;
  @override
  final String? refreshToken;
  @override
  final String tokenType;
  @override
  final int expiresIn;
  @override
  final User user;

  @override
  String toString() {
    return 'TokenResponse(accessToken: $accessToken, refreshToken: $refreshToken, tokenType: $tokenType, expiresIn: $expiresIn, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TokenResponseImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accessToken, refreshToken, tokenType, expiresIn, user);

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TokenResponseImplCopyWith<_$TokenResponseImpl> get copyWith =>
      __$$TokenResponseImplCopyWithImpl<_$TokenResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TokenResponseImplToJson(
      this,
    );
  }
}

abstract class _TokenResponse implements TokenResponse {
  const factory _TokenResponse(
      {required final String accessToken,
      final String? refreshToken,
      required final String tokenType,
      required final int expiresIn,
      required final User user}) = _$TokenResponseImpl;

  factory _TokenResponse.fromJson(Map<String, dynamic> json) =
      _$TokenResponseImpl.fromJson;

  @override
  String get accessToken;
  @override
  String? get refreshToken;
  @override
  String get tokenType;
  @override
  int get expiresIn;
  @override
  User get user;

  /// Create a copy of TokenResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TokenResponseImplCopyWith<_$TokenResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserResponse _$UserResponseFromJson(Map<String, dynamic> json) {
  return _UserResponse.fromJson(json);
}

/// @nodoc
mixin _$UserResponse {
  User get user => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this UserResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserResponseCopyWith<UserResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserResponseCopyWith<$Res> {
  factory $UserResponseCopyWith(
          UserResponse value, $Res Function(UserResponse) then) =
      _$UserResponseCopyWithImpl<$Res, UserResponse>;
  @useResult
  $Res call({User user, String? message});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class _$UserResponseCopyWithImpl<$Res, $Val extends UserResponse>
    implements $UserResponseCopyWith<$Res> {
  _$UserResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserResponseImplCopyWith<$Res>
    implements $UserResponseCopyWith<$Res> {
  factory _$$UserResponseImplCopyWith(
          _$UserResponseImpl value, $Res Function(_$UserResponseImpl) then) =
      __$$UserResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({User user, String? message});

  @override
  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$UserResponseImplCopyWithImpl<$Res>
    extends _$UserResponseCopyWithImpl<$Res, _$UserResponseImpl>
    implements _$$UserResponseImplCopyWith<$Res> {
  __$$UserResponseImplCopyWithImpl(
      _$UserResponseImpl _value, $Res Function(_$UserResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? message = freezed,
  }) {
    return _then(_$UserResponseImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserResponseImpl implements _UserResponse {
  const _$UserResponseImpl({required this.user, this.message});

  factory _$UserResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserResponseImplFromJson(json);

  @override
  final User user;
  @override
  final String? message;

  @override
  String toString() {
    return 'UserResponse(user: $user, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserResponseImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, user, message);

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserResponseImplCopyWith<_$UserResponseImpl> get copyWith =>
      __$$UserResponseImplCopyWithImpl<_$UserResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserResponseImplToJson(
      this,
    );
  }
}

abstract class _UserResponse implements UserResponse {
  const factory _UserResponse(
      {required final User user, final String? message}) = _$UserResponseImpl;

  factory _UserResponse.fromJson(Map<String, dynamic> json) =
      _$UserResponseImpl.fromJson;

  @override
  User get user;
  @override
  String? get message;

  /// Create a copy of UserResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserResponseImplCopyWith<_$UserResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserWithProfile _$UserWithProfileFromJson(Map<String, dynamic> json) {
  return _UserWithProfile.fromJson(json);
}

/// @nodoc
mixin _$UserWithProfile {
  User get user => throw _privateConstructorUsedError;
  RiderProfile? get riderProfile => throw _privateConstructorUsedError;
  DriverProfile? get driverProfile => throw _privateConstructorUsedError;

  /// Serializes this UserWithProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserWithProfileCopyWith<UserWithProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserWithProfileCopyWith<$Res> {
  factory $UserWithProfileCopyWith(
          UserWithProfile value, $Res Function(UserWithProfile) then) =
      _$UserWithProfileCopyWithImpl<$Res, UserWithProfile>;
  @useResult
  $Res call(
      {User user, RiderProfile? riderProfile, DriverProfile? driverProfile});

  $UserCopyWith<$Res> get user;
  $RiderProfileCopyWith<$Res>? get riderProfile;
  $DriverProfileCopyWith<$Res>? get driverProfile;
}

/// @nodoc
class _$UserWithProfileCopyWithImpl<$Res, $Val extends UserWithProfile>
    implements $UserWithProfileCopyWith<$Res> {
  _$UserWithProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? riderProfile = freezed,
    Object? driverProfile = freezed,
  }) {
    return _then(_value.copyWith(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      riderProfile: freezed == riderProfile
          ? _value.riderProfile
          : riderProfile // ignore: cast_nullable_to_non_nullable
              as RiderProfile?,
      driverProfile: freezed == driverProfile
          ? _value.driverProfile
          : driverProfile // ignore: cast_nullable_to_non_nullable
              as DriverProfile?,
    ) as $Val);
  }

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RiderProfileCopyWith<$Res>? get riderProfile {
    if (_value.riderProfile == null) {
      return null;
    }

    return $RiderProfileCopyWith<$Res>(_value.riderProfile!, (value) {
      return _then(_value.copyWith(riderProfile: value) as $Val);
    });
  }

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DriverProfileCopyWith<$Res>? get driverProfile {
    if (_value.driverProfile == null) {
      return null;
    }

    return $DriverProfileCopyWith<$Res>(_value.driverProfile!, (value) {
      return _then(_value.copyWith(driverProfile: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserWithProfileImplCopyWith<$Res>
    implements $UserWithProfileCopyWith<$Res> {
  factory _$$UserWithProfileImplCopyWith(_$UserWithProfileImpl value,
          $Res Function(_$UserWithProfileImpl) then) =
      __$$UserWithProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {User user, RiderProfile? riderProfile, DriverProfile? driverProfile});

  @override
  $UserCopyWith<$Res> get user;
  @override
  $RiderProfileCopyWith<$Res>? get riderProfile;
  @override
  $DriverProfileCopyWith<$Res>? get driverProfile;
}

/// @nodoc
class __$$UserWithProfileImplCopyWithImpl<$Res>
    extends _$UserWithProfileCopyWithImpl<$Res, _$UserWithProfileImpl>
    implements _$$UserWithProfileImplCopyWith<$Res> {
  __$$UserWithProfileImplCopyWithImpl(
      _$UserWithProfileImpl _value, $Res Function(_$UserWithProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
    Object? riderProfile = freezed,
    Object? driverProfile = freezed,
  }) {
    return _then(_$UserWithProfileImpl(
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
      riderProfile: freezed == riderProfile
          ? _value.riderProfile
          : riderProfile // ignore: cast_nullable_to_non_nullable
              as RiderProfile?,
      driverProfile: freezed == driverProfile
          ? _value.driverProfile
          : driverProfile // ignore: cast_nullable_to_non_nullable
              as DriverProfile?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserWithProfileImpl implements _UserWithProfile {
  const _$UserWithProfileImpl(
      {required this.user, this.riderProfile, this.driverProfile});

  factory _$UserWithProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserWithProfileImplFromJson(json);

  @override
  final User user;
  @override
  final RiderProfile? riderProfile;
  @override
  final DriverProfile? driverProfile;

  @override
  String toString() {
    return 'UserWithProfile(user: $user, riderProfile: $riderProfile, driverProfile: $driverProfile)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserWithProfileImpl &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.riderProfile, riderProfile) ||
                other.riderProfile == riderProfile) &&
            (identical(other.driverProfile, driverProfile) ||
                other.driverProfile == driverProfile));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, user, riderProfile, driverProfile);

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserWithProfileImplCopyWith<_$UserWithProfileImpl> get copyWith =>
      __$$UserWithProfileImplCopyWithImpl<_$UserWithProfileImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserWithProfileImplToJson(
      this,
    );
  }
}

abstract class _UserWithProfile implements UserWithProfile {
  const factory _UserWithProfile(
      {required final User user,
      final RiderProfile? riderProfile,
      final DriverProfile? driverProfile}) = _$UserWithProfileImpl;

  factory _UserWithProfile.fromJson(Map<String, dynamic> json) =
      _$UserWithProfileImpl.fromJson;

  @override
  User get user;
  @override
  RiderProfile? get riderProfile;
  @override
  DriverProfile? get driverProfile;

  /// Create a copy of UserWithProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserWithProfileImplCopyWith<_$UserWithProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RiderProfile _$RiderProfileFromJson(Map<String, dynamic> json) {
  return _RiderProfile.fromJson(json);
}

/// @nodoc
mixin _$RiderProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String? get emergencyContactName => throw _privateConstructorUsedError;
  String? get emergencyContactPhone => throw _privateConstructorUsedError;
  String? get preferredPaymentMethod => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this RiderProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiderProfileCopyWith<RiderProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileCopyWith<$Res> {
  factory $RiderProfileCopyWith(
          RiderProfile value, $Res Function(RiderProfile) then) =
      _$RiderProfileCopyWithImpl<$Res, RiderProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$RiderProfileCopyWithImpl<$Res, $Val extends RiderProfile>
    implements $RiderProfileCopyWith<$Res> {
  _$RiderProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RiderProfileImplCopyWith<$Res>
    implements $RiderProfileCopyWith<$Res> {
  factory _$$RiderProfileImplCopyWith(
          _$RiderProfileImpl value, $Res Function(_$RiderProfileImpl) then) =
      __$$RiderProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$RiderProfileImplCopyWithImpl<$Res>
    extends _$RiderProfileCopyWithImpl<$Res, _$RiderProfileImpl>
    implements _$$RiderProfileImplCopyWith<$Res> {
  __$$RiderProfileImplCopyWithImpl(
      _$RiderProfileImpl _value, $Res Function(_$RiderProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$RiderProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RiderProfileImpl implements _RiderProfile {
  const _$RiderProfileImpl(
      {required this.id,
      required this.userId,
      this.emergencyContactName,
      this.emergencyContactPhone,
      this.preferredPaymentMethod,
      required this.createdAt,
      required this.updatedAt});

  factory _$RiderProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiderProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String? emergencyContactName;
  @override
  final String? emergencyContactPhone;
  @override
  final String? preferredPaymentMethod;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'RiderProfile(id: $id, userId: $userId, emergencyContactName: $emergencyContactName, emergencyContactPhone: $emergencyContactPhone, preferredPaymentMethod: $preferredPaymentMethod, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.emergencyContactName, emergencyContactName) ||
                other.emergencyContactName == emergencyContactName) &&
            (identical(other.emergencyContactPhone, emergencyContactPhone) ||
                other.emergencyContactPhone == emergencyContactPhone) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, emergencyContactName,
      emergencyContactPhone, preferredPaymentMethod, createdAt, updatedAt);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      __$$RiderProfileImplCopyWithImpl<_$RiderProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiderProfileImplToJson(
      this,
    );
  }
}

abstract class _RiderProfile implements RiderProfile {
  const factory _RiderProfile(
      {required final String id,
      required final String userId,
      final String? emergencyContactName,
      final String? emergencyContactPhone,
      final String? preferredPaymentMethod,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$RiderProfileImpl;

  factory _RiderProfile.fromJson(Map<String, dynamic> json) =
      _$RiderProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String? get emergencyContactName;
  @override
  String? get emergencyContactPhone;
  @override
  String? get preferredPaymentMethod;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverProfile _$DriverProfileFromJson(Map<String, dynamic> json) {
  return _DriverProfile.fromJson(json);
}

/// @nodoc
mixin _$DriverProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError;
  String? get licenseNumber => throw _privateConstructorUsedError;
  String? get licenseExpiryDate => throw _privateConstructorUsedError;
  String? get vehicleMake => throw _privateConstructorUsedError;
  String? get vehicleModel => throw _privateConstructorUsedError;
  String? get vehicleYear => throw _privateConstructorUsedError;
  String? get vehicleColor => throw _privateConstructorUsedError;
  String? get vehiclePlateNumber => throw _privateConstructorUsedError;
  String? get vehicleInsuranceNumber => throw _privateConstructorUsedError;
  bool? get isApproved => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this DriverProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileCopyWith<DriverProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileCopyWith<$Res> {
  factory $DriverProfileCopyWith(
          DriverProfile value, $Res Function(DriverProfile) then) =
      _$DriverProfileCopyWithImpl<$Res, DriverProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber,
      bool? isApproved,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class _$DriverProfileCopyWithImpl<$Res, $Val extends DriverProfile>
    implements $DriverProfileCopyWith<$Res> {
  _$DriverProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
    Object? isApproved = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isApproved: freezed == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverProfileImplCopyWith<$Res>
    implements $DriverProfileCopyWith<$Res> {
  factory _$$DriverProfileImplCopyWith(
          _$DriverProfileImpl value, $Res Function(_$DriverProfileImpl) then) =
      __$$DriverProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber,
      bool? isApproved,
      DateTime createdAt,
      DateTime updatedAt});
}

/// @nodoc
class __$$DriverProfileImplCopyWithImpl<$Res>
    extends _$DriverProfileCopyWithImpl<$Res, _$DriverProfileImpl>
    implements _$$DriverProfileImplCopyWith<$Res> {
  __$$DriverProfileImplCopyWithImpl(
      _$DriverProfileImpl _value, $Res Function(_$DriverProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
    Object? isApproved = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$DriverProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      isApproved: freezed == isApproved
          ? _value.isApproved
          : isApproved // ignore: cast_nullable_to_non_nullable
              as bool?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileImpl implements _DriverProfile {
  const _$DriverProfileImpl(
      {required this.id,
      required this.userId,
      this.licenseNumber,
      this.licenseExpiryDate,
      this.vehicleMake,
      this.vehicleModel,
      this.vehicleYear,
      this.vehicleColor,
      this.vehiclePlateNumber,
      this.vehicleInsuranceNumber,
      this.isApproved,
      required this.createdAt,
      required this.updatedAt});

  factory _$DriverProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String? licenseNumber;
  @override
  final String? licenseExpiryDate;
  @override
  final String? vehicleMake;
  @override
  final String? vehicleModel;
  @override
  final String? vehicleYear;
  @override
  final String? vehicleColor;
  @override
  final String? vehiclePlateNumber;
  @override
  final String? vehicleInsuranceNumber;
  @override
  final bool? isApproved;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;

  @override
  String toString() {
    return 'DriverProfile(id: $id, userId: $userId, licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, vehicleMake: $vehicleMake, vehicleModel: $vehicleModel, vehicleYear: $vehicleYear, vehicleColor: $vehicleColor, vehiclePlateNumber: $vehiclePlateNumber, vehicleInsuranceNumber: $vehicleInsuranceNumber, isApproved: $isApproved, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.vehicleMake, vehicleMake) ||
                other.vehicleMake == vehicleMake) &&
            (identical(other.vehicleModel, vehicleModel) ||
                other.vehicleModel == vehicleModel) &&
            (identical(other.vehicleYear, vehicleYear) ||
                other.vehicleYear == vehicleYear) &&
            (identical(other.vehicleColor, vehicleColor) ||
                other.vehicleColor == vehicleColor) &&
            (identical(other.vehiclePlateNumber, vehiclePlateNumber) ||
                other.vehiclePlateNumber == vehiclePlateNumber) &&
            (identical(other.vehicleInsuranceNumber, vehicleInsuranceNumber) ||
                other.vehicleInsuranceNumber == vehicleInsuranceNumber) &&
            (identical(other.isApproved, isApproved) ||
                other.isApproved == isApproved) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      licenseNumber,
      licenseExpiryDate,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehicleColor,
      vehiclePlateNumber,
      vehicleInsuranceNumber,
      isApproved,
      createdAt,
      updatedAt);

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      __$$DriverProfileImplCopyWithImpl<_$DriverProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileImplToJson(
      this,
    );
  }
}

abstract class _DriverProfile implements DriverProfile {
  const factory _DriverProfile(
      {required final String id,
      required final String userId,
      final String? licenseNumber,
      final String? licenseExpiryDate,
      final String? vehicleMake,
      final String? vehicleModel,
      final String? vehicleYear,
      final String? vehicleColor,
      final String? vehiclePlateNumber,
      final String? vehicleInsuranceNumber,
      final bool? isApproved,
      required final DateTime createdAt,
      required final DateTime updatedAt}) = _$DriverProfileImpl;

  factory _DriverProfile.fromJson(Map<String, dynamic> json) =
      _$DriverProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId;
  @override
  String? get licenseNumber;
  @override
  String? get licenseExpiryDate;
  @override
  String? get vehicleMake;
  @override
  String? get vehicleModel;
  @override
  String? get vehicleYear;
  @override
  String? get vehicleColor;
  @override
  String? get vehiclePlateNumber;
  @override
  String? get vehicleInsuranceNumber;
  @override
  bool? get isApproved;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RiderProfileCreate _$RiderProfileCreateFromJson(Map<String, dynamic> json) {
  return _RiderProfileCreate.fromJson(json);
}

/// @nodoc
mixin _$RiderProfileCreate {
  String? get emergencyContactName => throw _privateConstructorUsedError;
  String? get emergencyContactPhone => throw _privateConstructorUsedError;
  String? get preferredPaymentMethod => throw _privateConstructorUsedError;

  /// Serializes this RiderProfileCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RiderProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiderProfileCreateCopyWith<RiderProfileCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileCreateCopyWith<$Res> {
  factory $RiderProfileCreateCopyWith(
          RiderProfileCreate value, $Res Function(RiderProfileCreate) then) =
      _$RiderProfileCreateCopyWithImpl<$Res, RiderProfileCreate>;
  @useResult
  $Res call(
      {String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod});
}

/// @nodoc
class _$RiderProfileCreateCopyWithImpl<$Res, $Val extends RiderProfileCreate>
    implements $RiderProfileCreateCopyWith<$Res> {
  _$RiderProfileCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_value.copyWith(
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RiderProfileCreateImplCopyWith<$Res>
    implements $RiderProfileCreateCopyWith<$Res> {
  factory _$$RiderProfileCreateImplCopyWith(_$RiderProfileCreateImpl value,
          $Res Function(_$RiderProfileCreateImpl) then) =
      __$$RiderProfileCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod});
}

/// @nodoc
class __$$RiderProfileCreateImplCopyWithImpl<$Res>
    extends _$RiderProfileCreateCopyWithImpl<$Res, _$RiderProfileCreateImpl>
    implements _$$RiderProfileCreateImplCopyWith<$Res> {
  __$$RiderProfileCreateImplCopyWithImpl(_$RiderProfileCreateImpl _value,
      $Res Function(_$RiderProfileCreateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_$RiderProfileCreateImpl(
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RiderProfileCreateImpl implements _RiderProfileCreate {
  const _$RiderProfileCreateImpl(
      {this.emergencyContactName,
      this.emergencyContactPhone,
      this.preferredPaymentMethod});

  factory _$RiderProfileCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiderProfileCreateImplFromJson(json);

  @override
  final String? emergencyContactName;
  @override
  final String? emergencyContactPhone;
  @override
  final String? preferredPaymentMethod;

  @override
  String toString() {
    return 'RiderProfileCreate(emergencyContactName: $emergencyContactName, emergencyContactPhone: $emergencyContactPhone, preferredPaymentMethod: $preferredPaymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileCreateImpl &&
            (identical(other.emergencyContactName, emergencyContactName) ||
                other.emergencyContactName == emergencyContactName) &&
            (identical(other.emergencyContactPhone, emergencyContactPhone) ||
                other.emergencyContactPhone == emergencyContactPhone) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, emergencyContactName,
      emergencyContactPhone, preferredPaymentMethod);

  /// Create a copy of RiderProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileCreateImplCopyWith<_$RiderProfileCreateImpl> get copyWith =>
      __$$RiderProfileCreateImplCopyWithImpl<_$RiderProfileCreateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiderProfileCreateImplToJson(
      this,
    );
  }
}

abstract class _RiderProfileCreate implements RiderProfileCreate {
  const factory _RiderProfileCreate(
      {final String? emergencyContactName,
      final String? emergencyContactPhone,
      final String? preferredPaymentMethod}) = _$RiderProfileCreateImpl;

  factory _RiderProfileCreate.fromJson(Map<String, dynamic> json) =
      _$RiderProfileCreateImpl.fromJson;

  @override
  String? get emergencyContactName;
  @override
  String? get emergencyContactPhone;
  @override
  String? get preferredPaymentMethod;

  /// Create a copy of RiderProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileCreateImplCopyWith<_$RiderProfileCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

RiderProfileUpdate _$RiderProfileUpdateFromJson(Map<String, dynamic> json) {
  return _RiderProfileUpdate.fromJson(json);
}

/// @nodoc
mixin _$RiderProfileUpdate {
  String? get emergencyContactName => throw _privateConstructorUsedError;
  String? get emergencyContactPhone => throw _privateConstructorUsedError;
  String? get preferredPaymentMethod => throw _privateConstructorUsedError;

  /// Serializes this RiderProfileUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RiderProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiderProfileUpdateCopyWith<RiderProfileUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileUpdateCopyWith<$Res> {
  factory $RiderProfileUpdateCopyWith(
          RiderProfileUpdate value, $Res Function(RiderProfileUpdate) then) =
      _$RiderProfileUpdateCopyWithImpl<$Res, RiderProfileUpdate>;
  @useResult
  $Res call(
      {String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod});
}

/// @nodoc
class _$RiderProfileUpdateCopyWithImpl<$Res, $Val extends RiderProfileUpdate>
    implements $RiderProfileUpdateCopyWith<$Res> {
  _$RiderProfileUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_value.copyWith(
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RiderProfileUpdateImplCopyWith<$Res>
    implements $RiderProfileUpdateCopyWith<$Res> {
  factory _$$RiderProfileUpdateImplCopyWith(_$RiderProfileUpdateImpl value,
          $Res Function(_$RiderProfileUpdateImpl) then) =
      __$$RiderProfileUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? emergencyContactName,
      String? emergencyContactPhone,
      String? preferredPaymentMethod});
}

/// @nodoc
class __$$RiderProfileUpdateImplCopyWithImpl<$Res>
    extends _$RiderProfileUpdateCopyWithImpl<$Res, _$RiderProfileUpdateImpl>
    implements _$$RiderProfileUpdateImplCopyWith<$Res> {
  __$$RiderProfileUpdateImplCopyWithImpl(_$RiderProfileUpdateImpl _value,
      $Res Function(_$RiderProfileUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emergencyContactName = freezed,
    Object? emergencyContactPhone = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_$RiderProfileUpdateImpl(
      emergencyContactName: freezed == emergencyContactName
          ? _value.emergencyContactName
          : emergencyContactName // ignore: cast_nullable_to_non_nullable
              as String?,
      emergencyContactPhone: freezed == emergencyContactPhone
          ? _value.emergencyContactPhone
          : emergencyContactPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RiderProfileUpdateImpl implements _RiderProfileUpdate {
  const _$RiderProfileUpdateImpl(
      {this.emergencyContactName,
      this.emergencyContactPhone,
      this.preferredPaymentMethod});

  factory _$RiderProfileUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiderProfileUpdateImplFromJson(json);

  @override
  final String? emergencyContactName;
  @override
  final String? emergencyContactPhone;
  @override
  final String? preferredPaymentMethod;

  @override
  String toString() {
    return 'RiderProfileUpdate(emergencyContactName: $emergencyContactName, emergencyContactPhone: $emergencyContactPhone, preferredPaymentMethod: $preferredPaymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileUpdateImpl &&
            (identical(other.emergencyContactName, emergencyContactName) ||
                other.emergencyContactName == emergencyContactName) &&
            (identical(other.emergencyContactPhone, emergencyContactPhone) ||
                other.emergencyContactPhone == emergencyContactPhone) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, emergencyContactName,
      emergencyContactPhone, preferredPaymentMethod);

  /// Create a copy of RiderProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileUpdateImplCopyWith<_$RiderProfileUpdateImpl> get copyWith =>
      __$$RiderProfileUpdateImplCopyWithImpl<_$RiderProfileUpdateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiderProfileUpdateImplToJson(
      this,
    );
  }
}

abstract class _RiderProfileUpdate implements RiderProfileUpdate {
  const factory _RiderProfileUpdate(
      {final String? emergencyContactName,
      final String? emergencyContactPhone,
      final String? preferredPaymentMethod}) = _$RiderProfileUpdateImpl;

  factory _RiderProfileUpdate.fromJson(Map<String, dynamic> json) =
      _$RiderProfileUpdateImpl.fromJson;

  @override
  String? get emergencyContactName;
  @override
  String? get emergencyContactPhone;
  @override
  String? get preferredPaymentMethod;

  /// Create a copy of RiderProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileUpdateImplCopyWith<_$RiderProfileUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverProfileCreate _$DriverProfileCreateFromJson(Map<String, dynamic> json) {
  return _DriverProfileCreate.fromJson(json);
}

/// @nodoc
mixin _$DriverProfileCreate {
  String? get licenseNumber => throw _privateConstructorUsedError;
  String? get licenseExpiryDate => throw _privateConstructorUsedError;
  String? get vehicleMake => throw _privateConstructorUsedError;
  String? get vehicleModel => throw _privateConstructorUsedError;
  String? get vehicleYear => throw _privateConstructorUsedError;
  String? get vehicleColor => throw _privateConstructorUsedError;
  String? get vehiclePlateNumber => throw _privateConstructorUsedError;
  String? get vehicleInsuranceNumber => throw _privateConstructorUsedError;

  /// Serializes this DriverProfileCreate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileCreateCopyWith<DriverProfileCreate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileCreateCopyWith<$Res> {
  factory $DriverProfileCreateCopyWith(
          DriverProfileCreate value, $Res Function(DriverProfileCreate) then) =
      _$DriverProfileCreateCopyWithImpl<$Res, DriverProfileCreate>;
  @useResult
  $Res call(
      {String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber});
}

/// @nodoc
class _$DriverProfileCreateCopyWithImpl<$Res, $Val extends DriverProfileCreate>
    implements $DriverProfileCreateCopyWith<$Res> {
  _$DriverProfileCreateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
  }) {
    return _then(_value.copyWith(
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverProfileCreateImplCopyWith<$Res>
    implements $DriverProfileCreateCopyWith<$Res> {
  factory _$$DriverProfileCreateImplCopyWith(_$DriverProfileCreateImpl value,
          $Res Function(_$DriverProfileCreateImpl) then) =
      __$$DriverProfileCreateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber});
}

/// @nodoc
class __$$DriverProfileCreateImplCopyWithImpl<$Res>
    extends _$DriverProfileCreateCopyWithImpl<$Res, _$DriverProfileCreateImpl>
    implements _$$DriverProfileCreateImplCopyWith<$Res> {
  __$$DriverProfileCreateImplCopyWithImpl(_$DriverProfileCreateImpl _value,
      $Res Function(_$DriverProfileCreateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
  }) {
    return _then(_$DriverProfileCreateImpl(
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileCreateImpl implements _DriverProfileCreate {
  const _$DriverProfileCreateImpl(
      {this.licenseNumber,
      this.licenseExpiryDate,
      this.vehicleMake,
      this.vehicleModel,
      this.vehicleYear,
      this.vehicleColor,
      this.vehiclePlateNumber,
      this.vehicleInsuranceNumber});

  factory _$DriverProfileCreateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileCreateImplFromJson(json);

  @override
  final String? licenseNumber;
  @override
  final String? licenseExpiryDate;
  @override
  final String? vehicleMake;
  @override
  final String? vehicleModel;
  @override
  final String? vehicleYear;
  @override
  final String? vehicleColor;
  @override
  final String? vehiclePlateNumber;
  @override
  final String? vehicleInsuranceNumber;

  @override
  String toString() {
    return 'DriverProfileCreate(licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, vehicleMake: $vehicleMake, vehicleModel: $vehicleModel, vehicleYear: $vehicleYear, vehicleColor: $vehicleColor, vehiclePlateNumber: $vehiclePlateNumber, vehicleInsuranceNumber: $vehicleInsuranceNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileCreateImpl &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.vehicleMake, vehicleMake) ||
                other.vehicleMake == vehicleMake) &&
            (identical(other.vehicleModel, vehicleModel) ||
                other.vehicleModel == vehicleModel) &&
            (identical(other.vehicleYear, vehicleYear) ||
                other.vehicleYear == vehicleYear) &&
            (identical(other.vehicleColor, vehicleColor) ||
                other.vehicleColor == vehicleColor) &&
            (identical(other.vehiclePlateNumber, vehiclePlateNumber) ||
                other.vehiclePlateNumber == vehiclePlateNumber) &&
            (identical(other.vehicleInsuranceNumber, vehicleInsuranceNumber) ||
                other.vehicleInsuranceNumber == vehicleInsuranceNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      licenseNumber,
      licenseExpiryDate,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehicleColor,
      vehiclePlateNumber,
      vehicleInsuranceNumber);

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileCreateImplCopyWith<_$DriverProfileCreateImpl> get copyWith =>
      __$$DriverProfileCreateImplCopyWithImpl<_$DriverProfileCreateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileCreateImplToJson(
      this,
    );
  }
}

abstract class _DriverProfileCreate implements DriverProfileCreate {
  const factory _DriverProfileCreate(
      {final String? licenseNumber,
      final String? licenseExpiryDate,
      final String? vehicleMake,
      final String? vehicleModel,
      final String? vehicleYear,
      final String? vehicleColor,
      final String? vehiclePlateNumber,
      final String? vehicleInsuranceNumber}) = _$DriverProfileCreateImpl;

  factory _DriverProfileCreate.fromJson(Map<String, dynamic> json) =
      _$DriverProfileCreateImpl.fromJson;

  @override
  String? get licenseNumber;
  @override
  String? get licenseExpiryDate;
  @override
  String? get vehicleMake;
  @override
  String? get vehicleModel;
  @override
  String? get vehicleYear;
  @override
  String? get vehicleColor;
  @override
  String? get vehiclePlateNumber;
  @override
  String? get vehicleInsuranceNumber;

  /// Create a copy of DriverProfileCreate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileCreateImplCopyWith<_$DriverProfileCreateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DriverProfileUpdate _$DriverProfileUpdateFromJson(Map<String, dynamic> json) {
  return _DriverProfileUpdate.fromJson(json);
}

/// @nodoc
mixin _$DriverProfileUpdate {
  String? get licenseNumber => throw _privateConstructorUsedError;
  String? get licenseExpiryDate => throw _privateConstructorUsedError;
  String? get vehicleMake => throw _privateConstructorUsedError;
  String? get vehicleModel => throw _privateConstructorUsedError;
  String? get vehicleYear => throw _privateConstructorUsedError;
  String? get vehicleColor => throw _privateConstructorUsedError;
  String? get vehiclePlateNumber => throw _privateConstructorUsedError;
  String? get vehicleInsuranceNumber => throw _privateConstructorUsedError;

  /// Serializes this DriverProfileUpdate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileUpdateCopyWith<DriverProfileUpdate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileUpdateCopyWith<$Res> {
  factory $DriverProfileUpdateCopyWith(
          DriverProfileUpdate value, $Res Function(DriverProfileUpdate) then) =
      _$DriverProfileUpdateCopyWithImpl<$Res, DriverProfileUpdate>;
  @useResult
  $Res call(
      {String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber});
}

/// @nodoc
class _$DriverProfileUpdateCopyWithImpl<$Res, $Val extends DriverProfileUpdate>
    implements $DriverProfileUpdateCopyWith<$Res> {
  _$DriverProfileUpdateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
  }) {
    return _then(_value.copyWith(
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverProfileUpdateImplCopyWith<$Res>
    implements $DriverProfileUpdateCopyWith<$Res> {
  factory _$$DriverProfileUpdateImplCopyWith(_$DriverProfileUpdateImpl value,
          $Res Function(_$DriverProfileUpdateImpl) then) =
      __$$DriverProfileUpdateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? licenseNumber,
      String? licenseExpiryDate,
      String? vehicleMake,
      String? vehicleModel,
      String? vehicleYear,
      String? vehicleColor,
      String? vehiclePlateNumber,
      String? vehicleInsuranceNumber});
}

/// @nodoc
class __$$DriverProfileUpdateImplCopyWithImpl<$Res>
    extends _$DriverProfileUpdateCopyWithImpl<$Res, _$DriverProfileUpdateImpl>
    implements _$$DriverProfileUpdateImplCopyWith<$Res> {
  __$$DriverProfileUpdateImplCopyWithImpl(_$DriverProfileUpdateImpl _value,
      $Res Function(_$DriverProfileUpdateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? licenseNumber = freezed,
    Object? licenseExpiryDate = freezed,
    Object? vehicleMake = freezed,
    Object? vehicleModel = freezed,
    Object? vehicleYear = freezed,
    Object? vehicleColor = freezed,
    Object? vehiclePlateNumber = freezed,
    Object? vehicleInsuranceNumber = freezed,
  }) {
    return _then(_$DriverProfileUpdateImpl(
      licenseNumber: freezed == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseExpiryDate: freezed == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleMake: freezed == vehicleMake
          ? _value.vehicleMake
          : vehicleMake // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleModel: freezed == vehicleModel
          ? _value.vehicleModel
          : vehicleModel // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleYear: freezed == vehicleYear
          ? _value.vehicleYear
          : vehicleYear // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleColor: freezed == vehicleColor
          ? _value.vehicleColor
          : vehicleColor // ignore: cast_nullable_to_non_nullable
              as String?,
      vehiclePlateNumber: freezed == vehiclePlateNumber
          ? _value.vehiclePlateNumber
          : vehiclePlateNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicleInsuranceNumber: freezed == vehicleInsuranceNumber
          ? _value.vehicleInsuranceNumber
          : vehicleInsuranceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileUpdateImpl implements _DriverProfileUpdate {
  const _$DriverProfileUpdateImpl(
      {this.licenseNumber,
      this.licenseExpiryDate,
      this.vehicleMake,
      this.vehicleModel,
      this.vehicleYear,
      this.vehicleColor,
      this.vehiclePlateNumber,
      this.vehicleInsuranceNumber});

  factory _$DriverProfileUpdateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileUpdateImplFromJson(json);

  @override
  final String? licenseNumber;
  @override
  final String? licenseExpiryDate;
  @override
  final String? vehicleMake;
  @override
  final String? vehicleModel;
  @override
  final String? vehicleYear;
  @override
  final String? vehicleColor;
  @override
  final String? vehiclePlateNumber;
  @override
  final String? vehicleInsuranceNumber;

  @override
  String toString() {
    return 'DriverProfileUpdate(licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, vehicleMake: $vehicleMake, vehicleModel: $vehicleModel, vehicleYear: $vehicleYear, vehicleColor: $vehicleColor, vehiclePlateNumber: $vehiclePlateNumber, vehicleInsuranceNumber: $vehicleInsuranceNumber)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileUpdateImpl &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.vehicleMake, vehicleMake) ||
                other.vehicleMake == vehicleMake) &&
            (identical(other.vehicleModel, vehicleModel) ||
                other.vehicleModel == vehicleModel) &&
            (identical(other.vehicleYear, vehicleYear) ||
                other.vehicleYear == vehicleYear) &&
            (identical(other.vehicleColor, vehicleColor) ||
                other.vehicleColor == vehicleColor) &&
            (identical(other.vehiclePlateNumber, vehiclePlateNumber) ||
                other.vehiclePlateNumber == vehiclePlateNumber) &&
            (identical(other.vehicleInsuranceNumber, vehicleInsuranceNumber) ||
                other.vehicleInsuranceNumber == vehicleInsuranceNumber));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      licenseNumber,
      licenseExpiryDate,
      vehicleMake,
      vehicleModel,
      vehicleYear,
      vehicleColor,
      vehiclePlateNumber,
      vehicleInsuranceNumber);

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileUpdateImplCopyWith<_$DriverProfileUpdateImpl> get copyWith =>
      __$$DriverProfileUpdateImplCopyWithImpl<_$DriverProfileUpdateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileUpdateImplToJson(
      this,
    );
  }
}

abstract class _DriverProfileUpdate implements DriverProfileUpdate {
  const factory _DriverProfileUpdate(
      {final String? licenseNumber,
      final String? licenseExpiryDate,
      final String? vehicleMake,
      final String? vehicleModel,
      final String? vehicleYear,
      final String? vehicleColor,
      final String? vehiclePlateNumber,
      final String? vehicleInsuranceNumber}) = _$DriverProfileUpdateImpl;

  factory _DriverProfileUpdate.fromJson(Map<String, dynamic> json) =
      _$DriverProfileUpdateImpl.fromJson;

  @override
  String? get licenseNumber;
  @override
  String? get licenseExpiryDate;
  @override
  String? get vehicleMake;
  @override
  String? get vehicleModel;
  @override
  String? get vehicleYear;
  @override
  String? get vehicleColor;
  @override
  String? get vehiclePlateNumber;
  @override
  String? get vehicleInsuranceNumber;

  /// Create a copy of DriverProfileUpdate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileUpdateImplCopyWith<_$DriverProfileUpdateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileImageUpload _$ProfileImageUploadFromJson(Map<String, dynamic> json) {
  return _ProfileImageUpload.fromJson(json);
}

/// @nodoc
mixin _$ProfileImageUpload {
  String get imageUrl => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;

  /// Serializes this ProfileImageUpload to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileImageUpload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileImageUploadCopyWith<ProfileImageUpload> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileImageUploadCopyWith<$Res> {
  factory $ProfileImageUploadCopyWith(
          ProfileImageUpload value, $Res Function(ProfileImageUpload) then) =
      _$ProfileImageUploadCopyWithImpl<$Res, ProfileImageUpload>;
  @useResult
  $Res call({String imageUrl, String? message});
}

/// @nodoc
class _$ProfileImageUploadCopyWithImpl<$Res, $Val extends ProfileImageUpload>
    implements $ProfileImageUploadCopyWith<$Res> {
  _$ProfileImageUploadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileImageUpload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
    Object? message = freezed,
  }) {
    return _then(_value.copyWith(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProfileImageUploadImplCopyWith<$Res>
    implements $ProfileImageUploadCopyWith<$Res> {
  factory _$$ProfileImageUploadImplCopyWith(_$ProfileImageUploadImpl value,
          $Res Function(_$ProfileImageUploadImpl) then) =
      __$$ProfileImageUploadImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String imageUrl, String? message});
}

/// @nodoc
class __$$ProfileImageUploadImplCopyWithImpl<$Res>
    extends _$ProfileImageUploadCopyWithImpl<$Res, _$ProfileImageUploadImpl>
    implements _$$ProfileImageUploadImplCopyWith<$Res> {
  __$$ProfileImageUploadImplCopyWithImpl(_$ProfileImageUploadImpl _value,
      $Res Function(_$ProfileImageUploadImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProfileImageUpload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imageUrl = null,
    Object? message = freezed,
  }) {
    return _then(_$ProfileImageUploadImpl(
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileImageUploadImpl implements _ProfileImageUpload {
  const _$ProfileImageUploadImpl({required this.imageUrl, this.message});

  factory _$ProfileImageUploadImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileImageUploadImplFromJson(json);

  @override
  final String imageUrl;
  @override
  final String? message;

  @override
  String toString() {
    return 'ProfileImageUpload(imageUrl: $imageUrl, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileImageUploadImpl &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, imageUrl, message);

  /// Create a copy of ProfileImageUpload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileImageUploadImplCopyWith<_$ProfileImageUploadImpl> get copyWith =>
      __$$ProfileImageUploadImplCopyWithImpl<_$ProfileImageUploadImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileImageUploadImplToJson(
      this,
    );
  }
}

abstract class _ProfileImageUpload implements ProfileImageUpload {
  const factory _ProfileImageUpload(
      {required final String imageUrl,
      final String? message}) = _$ProfileImageUploadImpl;

  factory _ProfileImageUpload.fromJson(Map<String, dynamic> json) =
      _$ProfileImageUploadImpl.fromJson;

  @override
  String get imageUrl;
  @override
  String? get message;

  /// Create a copy of ProfileImageUpload
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileImageUploadImplCopyWith<_$ProfileImageUploadImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
