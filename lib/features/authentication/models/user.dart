import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

/// User model representing a user in the system
@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
    String? profileImageUrl,
    required bool isActive,
    required bool isVerified,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

/// User creation model for registration
@freezed
class UserCreate with _$UserCreate {
  const factory UserCreate({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
  }) = _UserCreate;

  factory UserCreate.fromJson(Map<String, dynamic> json) => _$UserCreateFromJson(json);
}

/// User login model
@freezed
class UserLogin with _$UserLogin {
  const factory UserLogin({
    required String email,
    required String password,
  }) = _UserLogin;

  factory UserLogin.fromJson(Map<String, dynamic> json) => _$UserLoginFromJson(json);
}

/// User update model
@freezed
class UserUpdate with _$UserUpdate {
  const factory UserUpdate({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
  }) = _UserUpdate;

  factory UserUpdate.fromJson(Map<String, dynamic> json) => _$UserUpdateFromJson(json);
}

/// Token response model
@freezed
class TokenResponse with _$TokenResponse {
  const factory TokenResponse({
    required String accessToken,
    String? refreshToken,
    required String tokenType,
    required int expiresIn,
    required User user,
  }) = _TokenResponse;

  factory TokenResponse.fromJson(Map<String, dynamic> json) => _$TokenResponseFromJson(json);
}

/// User response model
@freezed
class UserResponse with _$UserResponse {
  const factory UserResponse({
    required User user,
    String? message,
  }) = _UserResponse;

  factory UserResponse.fromJson(Map<String, dynamic> json) => _$UserResponseFromJson(json);
}

/// User with profile model (includes type-specific data)
@freezed
class UserWithProfile with _$UserWithProfile {
  const factory UserWithProfile({
    required User user,
    RiderProfile? riderProfile,
    DriverProfile? driverProfile,
  }) = _UserWithProfile;

  factory UserWithProfile.fromJson(Map<String, dynamic> json) => _$UserWithProfileFromJson(json);
}

/// Rider profile model
@freezed
class RiderProfile with _$RiderProfile {
  const factory RiderProfile({
    required String id,
    required String userId,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? preferredPaymentMethod,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _RiderProfile;

  factory RiderProfile.fromJson(Map<String, dynamic> json) => _$RiderProfileFromJson(json);
}

/// Driver profile model
@freezed
class DriverProfile with _$DriverProfile {
  const factory DriverProfile({
    required String id,
    required String userId,
    String? licenseNumber,
    String? licenseExpiryDate,
    String? vehicleMake,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleColor,
    String? vehiclePlateNumber,
    String? vehicleInsuranceNumber,
    bool? isApproved,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _DriverProfile;

  factory DriverProfile.fromJson(Map<String, dynamic> json) => _$DriverProfileFromJson(json);
}

/// Rider profile creation model
@freezed
class RiderProfileCreate with _$RiderProfileCreate {
  const factory RiderProfileCreate({
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? preferredPaymentMethod,
  }) = _RiderProfileCreate;

  factory RiderProfileCreate.fromJson(Map<String, dynamic> json) => _$RiderProfileCreateFromJson(json);
}

/// Rider profile update model
@freezed
class RiderProfileUpdate with _$RiderProfileUpdate {
  const factory RiderProfileUpdate({
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? preferredPaymentMethod,
  }) = _RiderProfileUpdate;

  factory RiderProfileUpdate.fromJson(Map<String, dynamic> json) => _$RiderProfileUpdateFromJson(json);
}

/// Driver profile creation model
@freezed
class DriverProfileCreate with _$DriverProfileCreate {
  const factory DriverProfileCreate({
    String? licenseNumber,
    String? licenseExpiryDate,
    String? vehicleMake,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleColor,
    String? vehiclePlateNumber,
    String? vehicleInsuranceNumber,
  }) = _DriverProfileCreate;

  factory DriverProfileCreate.fromJson(Map<String, dynamic> json) => _$DriverProfileCreateFromJson(json);
}

/// Driver profile update model
@freezed
class DriverProfileUpdate with _$DriverProfileUpdate {
  const factory DriverProfileUpdate({
    String? licenseNumber,
    String? licenseExpiryDate,
    String? vehicleMake,
    String? vehicleModel,
    String? vehicleYear,
    String? vehicleColor,
    String? vehiclePlateNumber,
    String? vehicleInsuranceNumber,
  }) = _DriverProfileUpdate;

  factory DriverProfileUpdate.fromJson(Map<String, dynamic> json) => _$DriverProfileUpdateFromJson(json);
}

/// Profile image upload response
@freezed
class ProfileImageUpload with _$ProfileImageUpload {
  const factory ProfileImageUpload({
    required String imageUrl,
    String? message,
  }) = _ProfileImageUpload;

  factory ProfileImageUpload.fromJson(Map<String, dynamic> json) => _$ProfileImageUploadFromJson(json);
}

/// User type enum
enum UserType {
  @JsonValue('rider')
  rider,
  @JsonValue('driver')
  driver,
  @JsonValue('admin')
  admin,
}

/// Extension for UserType
extension UserTypeExtension on UserType {
  String get value {
    switch (this) {
      case UserType.rider:
        return 'rider';
      case UserType.driver:
        return 'driver';
      case UserType.admin:
        return 'admin';
    }
  }

  String get displayName {
    switch (this) {
      case UserType.rider:
        return 'Rider';
      case UserType.driver:
        return 'Driver';
      case UserType.admin:
        return 'Admin';
    }
  }
}

/// User extensions for additional functionality
extension UserExtensions on User {
  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get initials
  String get initials {
    final String firstInitial = firstName.isNotEmpty ? firstName[0].toUpperCase() : '';
    final String lastInitial = lastName.isNotEmpty ? lastName[0].toUpperCase() : '';
    return '$firstInitial$lastInitial';
  }

  /// Check if user is a rider
  bool get isRider => userType.toLowerCase() == 'rider';

  /// Check if user is a driver
  bool get isDriver => userType.toLowerCase() == 'driver';

  /// Check if user is an admin
  bool get isAdmin => userType.toLowerCase() == 'admin';

  /// Get user type enum
  UserType get userTypeEnum {
    switch (userType.toLowerCase()) {
      case 'rider':
        return UserType.rider;
      case 'driver':
        return UserType.driver;
      case 'admin':
        return UserType.admin;
      default:
        return UserType.rider;
    }
  }

  /// Check if profile is complete
  bool get isProfileComplete {
    return firstName.isNotEmpty &&
        lastName.isNotEmpty &&
        email.isNotEmpty &&
        phoneNumber != null &&
        phoneNumber!.isNotEmpty;
  }
}
