// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      userType: json['userType'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      dateOfBirth: json['dateOfBirth'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      isActive: json['isActive'] as bool,
      isVerified: json['isVerified'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'userType': instance.userType,
      'phoneNumber': instance.phoneNumber,
      'dateOfBirth': instance.dateOfBirth,
      'address': instance.address,
      'city': instance.city,
      'country': instance.country,
      'profileImageUrl': instance.profileImageUrl,
      'isActive': instance.isActive,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$UserCreateImpl _$$UserCreateImplFromJson(Map<String, dynamic> json) =>
    _$UserCreateImpl(
      email: json['email'] as String,
      password: json['password'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      userType: json['userType'] as String,
      phoneNumber: json['phoneNumber'] as String?,
    );

Map<String, dynamic> _$$UserCreateImplToJson(_$UserCreateImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'userType': instance.userType,
      'phoneNumber': instance.phoneNumber,
    };

_$UserLoginImpl _$$UserLoginImplFromJson(Map<String, dynamic> json) =>
    _$UserLoginImpl(
      email: json['email'] as String,
      password: json['password'] as String,
    );

Map<String, dynamic> _$$UserLoginImplToJson(_$UserLoginImpl instance) =>
    <String, dynamic>{
      'email': instance.email,
      'password': instance.password,
    };

_$UserUpdateImpl _$$UserUpdateImplFromJson(Map<String, dynamic> json) =>
    _$UserUpdateImpl(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      dateOfBirth: json['dateOfBirth'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
    );

Map<String, dynamic> _$$UserUpdateImplToJson(_$UserUpdateImpl instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'dateOfBirth': instance.dateOfBirth,
      'address': instance.address,
      'city': instance.city,
      'country': instance.country,
    };

_$TokenResponseImpl _$$TokenResponseImplFromJson(Map<String, dynamic> json) =>
    _$TokenResponseImpl(
      accessToken: json['accessToken'] as String,
      refreshToken: json['refreshToken'] as String?,
      tokenType: json['tokenType'] as String,
      expiresIn: (json['expiresIn'] as num).toInt(),
      user: User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$TokenResponseImplToJson(_$TokenResponseImpl instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'tokenType': instance.tokenType,
      'expiresIn': instance.expiresIn,
      'user': instance.user,
    };

_$UserResponseImpl _$$UserResponseImplFromJson(Map<String, dynamic> json) =>
    _$UserResponseImpl(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$UserResponseImplToJson(_$UserResponseImpl instance) =>
    <String, dynamic>{
      'user': instance.user,
      'message': instance.message,
    };

_$UserWithProfileImpl _$$UserWithProfileImplFromJson(
        Map<String, dynamic> json) =>
    _$UserWithProfileImpl(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      riderProfile: json['riderProfile'] == null
          ? null
          : RiderProfile.fromJson(json['riderProfile'] as Map<String, dynamic>),
      driverProfile: json['driverProfile'] == null
          ? null
          : DriverProfile.fromJson(
              json['driverProfile'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$UserWithProfileImplToJson(
        _$UserWithProfileImpl instance) =>
    <String, dynamic>{
      'user': instance.user,
      'riderProfile': instance.riderProfile,
      'driverProfile': instance.driverProfile,
    };

_$RiderProfileImpl _$$RiderProfileImplFromJson(Map<String, dynamic> json) =>
    _$RiderProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      emergencyContactName: json['emergencyContactName'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$RiderProfileImplToJson(_$RiderProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'emergencyContactName': instance.emergencyContactName,
      'emergencyContactPhone': instance.emergencyContactPhone,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$DriverProfileImpl _$$DriverProfileImplFromJson(Map<String, dynamic> json) =>
    _$DriverProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      licenseNumber: json['licenseNumber'] as String?,
      licenseExpiryDate: json['licenseExpiryDate'] as String?,
      vehicleMake: json['vehicleMake'] as String?,
      vehicleModel: json['vehicleModel'] as String?,
      vehicleYear: json['vehicleYear'] as String?,
      vehicleColor: json['vehicleColor'] as String?,
      vehiclePlateNumber: json['vehiclePlateNumber'] as String?,
      vehicleInsuranceNumber: json['vehicleInsuranceNumber'] as String?,
      isApproved: json['isApproved'] as bool?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$DriverProfileImplToJson(_$DriverProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate,
      'vehicleMake': instance.vehicleMake,
      'vehicleModel': instance.vehicleModel,
      'vehicleYear': instance.vehicleYear,
      'vehicleColor': instance.vehicleColor,
      'vehiclePlateNumber': instance.vehiclePlateNumber,
      'vehicleInsuranceNumber': instance.vehicleInsuranceNumber,
      'isApproved': instance.isApproved,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_$RiderProfileCreateImpl _$$RiderProfileCreateImplFromJson(
        Map<String, dynamic> json) =>
    _$RiderProfileCreateImpl(
      emergencyContactName: json['emergencyContactName'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
    );

Map<String, dynamic> _$$RiderProfileCreateImplToJson(
        _$RiderProfileCreateImpl instance) =>
    <String, dynamic>{
      'emergencyContactName': instance.emergencyContactName,
      'emergencyContactPhone': instance.emergencyContactPhone,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
    };

_$RiderProfileUpdateImpl _$$RiderProfileUpdateImplFromJson(
        Map<String, dynamic> json) =>
    _$RiderProfileUpdateImpl(
      emergencyContactName: json['emergencyContactName'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
    );

Map<String, dynamic> _$$RiderProfileUpdateImplToJson(
        _$RiderProfileUpdateImpl instance) =>
    <String, dynamic>{
      'emergencyContactName': instance.emergencyContactName,
      'emergencyContactPhone': instance.emergencyContactPhone,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
    };

_$DriverProfileCreateImpl _$$DriverProfileCreateImplFromJson(
        Map<String, dynamic> json) =>
    _$DriverProfileCreateImpl(
      licenseNumber: json['licenseNumber'] as String?,
      licenseExpiryDate: json['licenseExpiryDate'] as String?,
      vehicleMake: json['vehicleMake'] as String?,
      vehicleModel: json['vehicleModel'] as String?,
      vehicleYear: json['vehicleYear'] as String?,
      vehicleColor: json['vehicleColor'] as String?,
      vehiclePlateNumber: json['vehiclePlateNumber'] as String?,
      vehicleInsuranceNumber: json['vehicleInsuranceNumber'] as String?,
    );

Map<String, dynamic> _$$DriverProfileCreateImplToJson(
        _$DriverProfileCreateImpl instance) =>
    <String, dynamic>{
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate,
      'vehicleMake': instance.vehicleMake,
      'vehicleModel': instance.vehicleModel,
      'vehicleYear': instance.vehicleYear,
      'vehicleColor': instance.vehicleColor,
      'vehiclePlateNumber': instance.vehiclePlateNumber,
      'vehicleInsuranceNumber': instance.vehicleInsuranceNumber,
    };

_$DriverProfileUpdateImpl _$$DriverProfileUpdateImplFromJson(
        Map<String, dynamic> json) =>
    _$DriverProfileUpdateImpl(
      licenseNumber: json['licenseNumber'] as String?,
      licenseExpiryDate: json['licenseExpiryDate'] as String?,
      vehicleMake: json['vehicleMake'] as String?,
      vehicleModel: json['vehicleModel'] as String?,
      vehicleYear: json['vehicleYear'] as String?,
      vehicleColor: json['vehicleColor'] as String?,
      vehiclePlateNumber: json['vehiclePlateNumber'] as String?,
      vehicleInsuranceNumber: json['vehicleInsuranceNumber'] as String?,
    );

Map<String, dynamic> _$$DriverProfileUpdateImplToJson(
        _$DriverProfileUpdateImpl instance) =>
    <String, dynamic>{
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate,
      'vehicleMake': instance.vehicleMake,
      'vehicleModel': instance.vehicleModel,
      'vehicleYear': instance.vehicleYear,
      'vehicleColor': instance.vehicleColor,
      'vehiclePlateNumber': instance.vehiclePlateNumber,
      'vehicleInsuranceNumber': instance.vehicleInsuranceNumber,
    };

_$ProfileImageUploadImpl _$$ProfileImageUploadImplFromJson(
        Map<String, dynamic> json) =>
    _$ProfileImageUploadImpl(
      imageUrl: json['imageUrl'] as String,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$$ProfileImageUploadImplToJson(
        _$ProfileImageUploadImpl instance) =>
    <String, dynamic>{
      'imageUrl': instance.imageUrl,
      'message': instance.message,
    };
