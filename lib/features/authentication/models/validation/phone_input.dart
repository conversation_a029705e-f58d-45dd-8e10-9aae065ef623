import 'package:formz/formz.dart';

/// Phone validation error types
enum PhoneValidationError {
  empty,
  invalid,
  tooShort,
  tooLong,
}

/// Phone input validation using Formz
class PhoneInput extends FormzInput<String, PhoneValidationError> {
  const PhoneInput.pure() : super.pure('');
  const PhoneInput.dirty([super.value = '']) : super.dirty();

  // Caribbean phone number patterns (including St. Lucia)
  static final RegExp _phoneRegExp = RegExp(
    r'^(\+1[-.\s]?)?(\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4})$',
  );

  @override
  PhoneValidationError? validator(String value) {
    final String cleanValue = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    if (value.isEmpty) {
      return PhoneValidationError.empty;
    } else if (cleanValue.length < 10) {
      return PhoneValidationError.tooShort;
    } else if (cleanValue.length > 15) {
      return PhoneValidationError.tooLong;
    } else if (!_phoneRegExp.hasMatch(value)) {
      return PhoneValidationError.invalid;
    }
    return null;
  }
}

/// Extension for phone validation error messages
extension PhoneValidationErrorExtension on PhoneValidationError {
  String get message {
    switch (this) {
      case PhoneValidationError.empty:
        return 'Phone number is required';
      case PhoneValidationError.invalid:
        return 'Please enter a valid phone number';
      case PhoneValidationError.tooShort:
        return 'Phone number is too short';
      case PhoneValidationError.tooLong:
        return 'Phone number is too long';
    }
  }
}
