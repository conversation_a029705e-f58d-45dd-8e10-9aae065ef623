import 'package:formz/formz.dart';

/// Password validation error types
enum PasswordValidationError {
  empty,
  tooShort,
  noUppercase,
  noLowercase,
  noNumber,
  noSpecialChar,
}

/// Password input validation using Formz
class PasswordInput extends FormzInput<String, PasswordValidationError> {
  const PasswordInput.pure() : super.pure('');
  const PasswordInput.dirty([super.value = '']) : super.dirty();

  static final RegExp _uppercaseRegExp = RegExp(r'[A-Z]');
  static final RegExp _lowercaseRegExp = RegExp(r'[a-z]');
  static final RegExp _numberRegExp = RegExp(r'[0-9]');
  static final RegExp _specialCharRegExp = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  @override
  PasswordValidationError? validator(String value) {
    if (value.isEmpty) {
      return PasswordValidationError.empty;
    } else if (value.length < 8) {
      return PasswordValidationError.tooShort;
    } else if (!_uppercaseRegExp.hasMatch(value)) {
      return PasswordValidationError.noUppercase;
    } else if (!_lowercaseRegExp.hasMatch(value)) {
      return PasswordValidationError.noLowercase;
    } else if (!_numberRegExp.hasMatch(value)) {
      return PasswordValidationError.noNumber;
    } else if (!_specialCharRegExp.hasMatch(value)) {
      return PasswordValidationError.noSpecialChar;
    }
    return null;
  }
}

/// Extension for password validation error messages
extension PasswordValidationErrorExtension on PasswordValidationError {
  String get message {
    switch (this) {
      case PasswordValidationError.empty:
        return 'Password is required';
      case PasswordValidationError.tooShort:
        return 'Password must be at least 8 characters long';
      case PasswordValidationError.noUppercase:
        return 'Password must contain at least one uppercase letter';
      case PasswordValidationError.noLowercase:
        return 'Password must contain at least one lowercase letter';
      case PasswordValidationError.noNumber:
        return 'Password must contain at least one number';
      case PasswordValidationError.noSpecialChar:
        return 'Password must contain at least one special character';
    }
  }
}
