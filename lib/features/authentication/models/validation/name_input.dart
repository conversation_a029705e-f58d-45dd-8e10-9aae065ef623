import 'package:formz/formz.dart';

/// Name validation error types
enum NameValidationError {
  empty,
  tooShort,
  invalid,
}

/// Name input validation using Formz
class NameInput extends FormzInput<String, NameValidationError> {
  const NameInput.pure() : super.pure('');
  const NameInput.dirty([super.value = '']) : super.dirty();

  static final RegExp _nameRegExp = RegExp(r"^[a-zA-Z\s\-'\.]+$");

  @override
  NameValidationError? validator(String value) {
    final String trimmedValue = value.trim();
    
    if (trimmedValue.isEmpty) {
      return NameValidationError.empty;
    } else if (trimmedValue.length < 2) {
      return NameValidationError.tooShort;
    } else if (!_nameRegExp.hasMatch(trimmedValue)) {
      return NameValidationError.invalid;
    }
    return null;
  }
}

/// Extension for name validation error messages
extension NameValidationErrorExtension on NameValidationError {
  String get message {
    switch (this) {
      case NameValidationError.empty:
        return 'Name is required';
      case NameValidationError.tooShort:
        return 'Name must be at least 2 characters long';
      case NameValidationError.invalid:
        return 'Name can only contain letters, spaces, hyphens, apostrophes, and periods';
    }
  }
}
