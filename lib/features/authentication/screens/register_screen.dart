import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/theme/index.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../controllers/auth_controller.dart';
import '../models/user.dart';
import '../models/validation/email_input.dart';
import '../models/validation/name_input.dart';
import '../models/validation/password_input.dart';
import '../models/validation/phone_input.dart';

/// Registration screen for new user signup
class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  
  UserType _selectedUserType = UserType.rider;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _acceptTerms = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authControllerProvider);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Create Account'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: authState.isLoading,
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpacing.containerPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Header
                  _buildHeader(),
                  
                  const SizedBox(height: AppSpacing.xl),
                  
                  // User type selection
                  _buildUserTypeSelection(),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Registration form
                  _buildRegistrationForm(),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Terms and conditions
                  _buildTermsAndConditions(),
                  
                  const SizedBox(height: AppSpacing.xl),
                  
                  // Register button
                  _buildRegisterButton(),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Sign in link
                  _buildSignInLink(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Join Lucian Rides',
          style: AppTextStyles.heading2,
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppSpacing.sm),
        
        Text(
          'Create your account to get started',
          style: AppTextStyles.body1.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'I want to',
          style: AppTextStyles.inputLabel,
        ),
        
        const SizedBox(height: AppSpacing.sm),
        
        Row(
          children: [
            Expanded(
              child: _buildUserTypeCard(
                userType: UserType.rider,
                title: 'Book Rides',
                subtitle: 'Find and book rides',
                icon: Icons.person,
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            Expanded(
              child: _buildUserTypeCard(
                userType: UserType.driver,
                title: 'Drive & Earn',
                subtitle: 'Offer rides to passengers',
                icon: Icons.directions_car,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUserTypeCard({
    required UserType userType,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    final bool isSelected = _selectedUserType == userType;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedUserType = userType;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryWithOpacity(0.1) : AppColors.surface,
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: AppSpacing.iconLg,
              color: isSelected ? AppColors.primary : AppColors.onSurfaceVariant,
            ),
            
            const SizedBox(height: AppSpacing.sm),
            
            Text(
              title,
              style: AppTextStyles.buttonMedium.copyWith(
                color: isSelected ? AppColors.primary : AppColors.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: AppSpacing.xs),
            
            Text(
              subtitle,
              style: AppTextStyles.caption.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationForm() {
    return Column(
      children: [
        // First name and last name
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _firstNameController,
                label: 'First Name',
                hintText: 'Enter first name',
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.person_outlined,
                validator: (value) {
                  final nameInput = NameInput.dirty(value ?? '');
                  return nameInput.error?.message;
                },
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            Expanded(
              child: CustomTextField(
                controller: _lastNameController,
                label: 'Last Name',
                hintText: 'Enter last name',
                textInputAction: TextInputAction.next,
                prefixIcon: Icons.person_outlined,
                validator: (value) {
                  final nameInput = NameInput.dirty(value ?? '');
                  return nameInput.error?.message;
                },
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Email field
        CustomTextField(
          controller: _emailController,
          label: 'Email',
          hintText: 'Enter your email',
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.email_outlined,
          validator: (value) {
            final emailInput = EmailInput.dirty(value ?? '');
            return emailInput.error?.message;
          },
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Phone field
        CustomTextField(
          controller: _phoneController,
          label: 'Phone Number',
          hintText: 'Enter your phone number',
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.phone_outlined,
          validator: (value) {
            final phoneInput = PhoneInput.dirty(value ?? '');
            return phoneInput.error?.message;
          },
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Password field
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          hintText: 'Create a password',
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          validator: (value) {
            final passwordInput = PasswordInput.dirty(value ?? '');
            return passwordInput.error?.message;
          },
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Confirm password field
        CustomTextField(
          controller: _confirmPasswordController,
          label: 'Confirm Password',
          hintText: 'Confirm your password',
          obscureText: _obscureConfirmPassword,
          textInputAction: TextInputAction.done,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureConfirmPassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            ),
            onPressed: () {
              setState(() {
                _obscureConfirmPassword = !_obscureConfirmPassword;
              });
            },
          ),
          validator: (value) {
            if (value != _passwordController.text) {
              return 'Passwords do not match';
            }
            return null;
          },
          onFieldSubmitted: (_) => _handleRegister(),
        ),
      ],
    );
  }

  Widget _buildTermsAndConditions() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
        ),
        
        const SizedBox(width: AppSpacing.sm),
        
        Expanded(
          child: RichText(
            text: TextSpan(
              style: AppTextStyles.body2,
              children: [
                const TextSpan(text: 'I agree to the '),
                TextSpan(
                  text: 'Terms of Service',
                  style: AppTextStyles.link,
                ),
                const TextSpan(text: ' and '),
                TextSpan(
                  text: 'Privacy Policy',
                  style: AppTextStyles.link,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return CustomButton(
      text: 'Create Account',
      onPressed: _acceptTerms ? _handleRegister : null,
      isLoading: ref.watch(authControllerProvider).isLoading,
    );
  }

  Widget _buildSignInLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Already have an account? ',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Sign In',
            style: AppTextStyles.link,
          ),
        ),
      ],
    );
  }

  void _handleRegister() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_acceptTerms) {
      _showErrorSnackBar('Please accept the terms and conditions');
      return;
    }

    final String firstName = _firstNameController.text.trim();
    final String lastName = _lastNameController.text.trim();
    final String email = _emailController.text.trim();
    final String phone = _phoneController.text.trim();
    final String password = _passwordController.text;

    // Perform registration
    ref.read(authControllerProvider.notifier).register(
      email: email,
      password: password,
      firstName: firstName,
      lastName: lastName,
      userType: _selectedUserType.value,
      phoneNumber: phone.isNotEmpty ? phone : null,
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
