import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/theme/index.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../controllers/auth_controller.dart';
import '../models/validation/email_input.dart';
import '../models/validation/password_input.dart';

/// Login screen for user authentication
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  
  bool _rememberMe = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authControllerProvider);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: LoadingOverlay(
        isLoading: authState.isLoading,
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpacing.containerPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: AppSpacing.xxxl),
                  
                  // Logo and title
                  _buildHeader(),
                  
                  const SizedBox(height: AppSpacing.xxxl),
                  
                  // Login form
                  _buildLoginForm(),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Remember me checkbox
                  _buildRememberMe(),
                  
                  const SizedBox(height: AppSpacing.xl),
                  
                  // Login button
                  _buildLoginButton(),
                  
                  const SizedBox(height: AppSpacing.lg),
                  
                  // Forgot password link
                  _buildForgotPasswordLink(),
                  
                  const SizedBox(height: AppSpacing.xxxl),
                  
                  // Sign up link
                  _buildSignUpLink(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App logo placeholder
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(AppSpacing.radiusXl),
          ),
          child: const Icon(
            Icons.directions_car,
            color: AppColors.white,
            size: AppSpacing.iconXl,
          ),
        ),
        
        const SizedBox(height: AppSpacing.lg),
        
        Text(
          'Welcome Back',
          style: AppTextStyles.heading1,
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppSpacing.sm),
        
        Text(
          'Sign in to your account',
          style: AppTextStyles.body1.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        // Email field
        CustomTextField(
          controller: _emailController,
          label: 'Email',
          hintText: 'Enter your email',
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          prefixIcon: Icons.email_outlined,
          validator: (value) {
            final emailInput = EmailInput.dirty(value ?? '');
            return emailInput.error?.message;
          },
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Password field
        CustomTextField(
          controller: _passwordController,
          label: 'Password',
          hintText: 'Enter your password',
          obscureText: _obscurePassword,
          textInputAction: TextInputAction.done,
          prefixIcon: Icons.lock_outlined,
          suffixIcon: IconButton(
            icon: Icon(
              _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            ),
            onPressed: () {
              setState(() {
                _obscurePassword = !_obscurePassword;
              });
            },
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Password is required';
            }
            return null;
          },
          onFieldSubmitted: (_) => _handleLogin(),
        ),
      ],
    );
  }

  Widget _buildRememberMe() {
    return Row(
      children: [
        Checkbox(
          value: _rememberMe,
          onChanged: (value) {
            setState(() {
              _rememberMe = value ?? false;
            });
          },
        ),
        
        const SizedBox(width: AppSpacing.sm),
        
        Text(
          'Remember me',
          style: AppTextStyles.body2,
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return CustomButton(
      text: 'Sign In',
      onPressed: _handleLogin,
      isLoading: ref.watch(authControllerProvider).isLoading,
    );
  }

  Widget _buildForgotPasswordLink() {
    return TextButton(
      onPressed: _handleForgotPassword,
      child: Text(
        'Forgot Password?',
        style: AppTextStyles.link,
      ),
    );
  }

  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: AppTextStyles.body2.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        
        TextButton(
          onPressed: _handleSignUp,
          child: Text(
            'Sign Up',
            style: AppTextStyles.link,
          ),
        ),
      ],
    );
  }

  void _handleLogin() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final String email = _emailController.text.trim();
    final String password = _passwordController.text;

    // Validate inputs
    final emailInput = EmailInput.dirty(email);
    final passwordInput = PasswordInput.dirty(password);

    if (emailInput.error != null) {
      _showErrorSnackBar(emailInput.error!.message);
      return;
    }

    if (passwordInput.error != null) {
      _showErrorSnackBar(passwordInput.error!.message);
      return;
    }

    // Perform login
    ref.read(authControllerProvider.notifier).login(
      email: email,
      password: password,
      rememberMe: _rememberMe,
    );
  }

  void _handleForgotPassword() {
    // TODO: Navigate to forgot password screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Forgot password feature coming soon'),
      ),
    );
  }

  void _handleSignUp() {
    // TODO: Navigate to registration screen
    Navigator.of(context).pushNamed('/register');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }
}
