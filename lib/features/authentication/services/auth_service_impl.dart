import 'package:logger/logger.dart';

import '../../../core/exceptions/api_exception.dart';
import '../../../core/models/api_response.dart';
import '../../../core/repositories/auth_repository.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/token_service.dart';
import '../models/user.dart';

/// Implementation of authentication service with business logic
class AuthServiceImpl {
  AuthServiceImpl({
    required AuthRepository authRepository,
    required TokenService tokenService,
    required StorageService storageService,
  }) : _authRepository = authRepository,
       _tokenService = tokenService,
       _storageService = storageService;

  final AuthRepository _authRepository;
  final TokenService _tokenService;
  final StorageService _storageService;
  final Logger _logger = Logger();

  /// Register a new user
  Future<ApiResponse<User>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String userType,
    String? phoneNumber,
  }) async {
    _logger.i('Registering user: $email');

    try {
      final Map<String, dynamic> response = await _authRepository.register(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        userType: userType,
        phoneNumber: phoneNumber,
      );

      // Handle authentication success
      _handleAuthenticationSuccess(response);

      // Extract user data
      final Map<String, dynamic> userData =
          response['user'] as Map<String, dynamic>;
      final User user = User.fromJson(userData);
      return ApiResponse.success(user);
    } catch (e) {
      _logger.e('Registration failed: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Registration failed',
          context: 'register',
          originalError: e,
        ),
      );
    }
  }

  /// Login user
  Future<ApiResponse<User>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _logger.i('Logging in user: $email');

    try {
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .login(email: email, password: password, rememberMe: rememberMe);

      return response.map((data) {
        // Handle authentication success
        _handleAuthenticationSuccess(data);

        // Store remember me preference
        _storageService.setRememberMe(rememberMe);

        // Extract user data
        final Map<String, dynamic> userData =
            data['user'] as Map<String, dynamic>;
        return User.fromJson(userData);
      });
    } catch (e) {
      _logger.e('Login failed: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Login failed',
          context: 'login',
          originalError: e,
        ),
      );
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    _logger.i('Logging out user');

    try {
      // Call API logout endpoint
      await _authRepository.logout();

      // Clear all local data
      await _clearUserData();

      _logger.i('User logged out successfully');
      return ApiResponse.success(null);
    } catch (e) {
      _logger.e('Logout error: $e');

      // Clear local data even if API call fails
      await _clearUserData();

      return ApiResponse.success(null);
    }
  }

  /// Get current user profile
  Future<ApiResponse<User>> getCurrentUser() async {
    _logger.d('Getting current user profile');

    try {
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .getProfile();

      return response.map((data) {
        return User.fromJson(data);
      });
    } catch (e) {
      _logger.e('Failed to get current user: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Failed to get user profile',
          context: 'getCurrentUser',
          originalError: e,
        ),
      );
    }
  }

  /// Update user profile
  Future<ApiResponse<User>> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? dateOfBirth,
    String? address,
    String? city,
    String? country,
  }) async {
    _logger.i('Updating user profile');

    try {
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .updateProfile(
            firstName: firstName,
            lastName: lastName,
            phoneNumber: phoneNumber,
            dateOfBirth: dateOfBirth,
            address: address,
            city: city,
            country: country,
          );

      return response.map((data) {
        final User user = User.fromJson(data);

        // Update stored user data
        _storageService.storeUserData(data);

        return user;
      });
    } catch (e) {
      _logger.e('Failed to update profile: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Failed to update profile',
          context: 'updateProfile',
          originalError: e,
        ),
      );
    }
  }

  /// Get complete user profile with type-specific data
  Future<ApiResponse<UserWithProfile>> getCompleteProfile() async {
    _logger.d('Getting complete user profile');

    try {
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .getCompleteProfile();

      return response.map((data) {
        return UserWithProfile.fromJson(data);
      });
    } catch (e) {
      _logger.e('Failed to get complete profile: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Failed to get complete profile',
          context: 'getCompleteProfile',
          originalError: e,
        ),
      );
    }
  }

  /// Upload profile image
  Future<ApiResponse<String>> uploadProfileImage({
    required String filePath,
  }) async {
    _logger.i('Uploading profile image');

    try {
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .uploadProfileImage(filePath: filePath);

      return response.map((data) {
        return data['image_url'] as String;
      });
    } catch (e) {
      _logger.e('Failed to upload profile image: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Failed to upload profile image',
          context: 'uploadProfileImage',
          originalError: e,
        ),
      );
    }
  }

  /// Delete profile image
  Future<ApiResponse<void>> deleteProfileImage() async {
    _logger.i('Deleting profile image');

    try {
      await _authRepository.deleteProfileImage();
      return ApiResponse.success(null);
    } catch (e) {
      _logger.e('Failed to delete profile image: $e');
      return ApiResponse.error(
        ApiException(
          message: 'Failed to delete profile image',
          context: 'deleteProfileImage',
          originalError: e,
        ),
      );
    }
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    return await _tokenService.isAuthenticated();
  }

  /// Verify current token
  Future<ApiResponse<bool>> verifyToken() async {
    _logger.d('Verifying token');

    try {
      // First check if we have a token locally
      final bool hasValidToken = await _tokenService.hasValidAccessToken();
      if (!hasValidToken) {
        return ApiResponse.success(false);
      }

      // Verify with server
      final ApiResponse<Map<String, dynamic>> response = await _authRepository
          .verifyToken();

      return response.map((data) {
        return data['valid'] as bool? ?? true;
      });
    } catch (e) {
      _logger.e('Token verification failed: $e');
      return ApiResponse.success(false);
    }
  }

  /// Get current user ID from token
  Future<String?> getCurrentUserId() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.getUserIdFromToken(token);
    }
    return null;
  }

  /// Get current user email from token
  Future<String?> getCurrentUserEmail() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.getUserEmailFromToken(token);
    }
    return null;
  }

  /// Check if token needs refresh
  Future<bool> shouldRefreshToken() async {
    final String? token = await _tokenService.getAccessToken();
    if (token != null) {
      return _tokenService.shouldRefreshToken(token);
    }
    return false;
  }

  /// Get stored user data
  Future<User?> getStoredUser() async {
    try {
      final Map<String, dynamic>? userData = await _storageService
          .getUserData();
      if (userData != null) {
        return User.fromJson(userData);
      }
    } catch (e) {
      _logger.e('Failed to get stored user: $e');
    }
    return null;
  }

  /// Check if remember me is enabled
  bool getRememberMe() {
    return _storageService.getRememberMe();
  }

  /// Handle successful authentication response
  Future<void> _handleAuthenticationSuccess(
    Map<String, dynamic> responseData,
  ) async {
    try {
      // Extract tokens from response
      final String? accessToken = responseData['access_token'] as String?;
      final String? refreshToken = responseData['refresh_token'] as String?;

      if (accessToken != null) {
        // Store tokens
        await _tokenService.storeTokens(
          accessToken: accessToken,
          refreshToken: refreshToken,
        );

        // Store user data if available
        final Map<String, dynamic>? userData =
            responseData['user'] as Map<String, dynamic>?;
        if (userData != null) {
          await _storageService.storeUserData(userData);
        }

        _logger.d('Authentication data stored successfully');
      } else {
        _logger.w('No access token in authentication response');
      }
    } catch (e) {
      _logger.e('Error handling authentication success: $e');
      rethrow;
    }
  }

  /// Clear all user data
  Future<void> _clearUserData() async {
    try {
      await _tokenService.clearTokens();
      await _storageService.clearAuthData();
      _logger.d('User data cleared successfully');
    } catch (e) {
      _logger.e('Error clearing user data: $e');
    }
  }
}
