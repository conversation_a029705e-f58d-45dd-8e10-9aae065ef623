// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rider_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RiderProfile _$RiderProfileFromJson(Map<String, dynamic> json) {
  return _RiderProfile.fromJson(json);
}

/// @nodoc
mixin _$RiderProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId => throw _privateConstructorUsedError; // Emergency contacts
  List<EmergencyContact> get emergencyContacts =>
      throw _privateConstructorUsedError; // Preferences
  bool get allowSharedRides => throw _privateConstructorUsedError;
  bool get allowPetFriendlyRides => throw _privateConstructorUsedError;
  bool get preferFemaleDrivers => throw _privateConstructorUsedError;
  bool get accessibilityNeeds =>
      throw _privateConstructorUsedError; // Accessibility details
  String? get accessibilityDetails =>
      throw _privateConstructorUsedError; // Favorite locations
  List<FavoriteLocation> get favoriteLocations =>
      throw _privateConstructorUsedError; // Payment preferences
  String? get preferredPaymentMethod =>
      throw _privateConstructorUsedError; // Ride history stats
  int get totalRides => throw _privateConstructorUsedError;
  double get averageRating => throw _privateConstructorUsedError;
  int get totalSpent =>
      throw _privateConstructorUsedError; // Profile completion
  bool get isProfileComplete => throw _privateConstructorUsedError;
  double get completionPercentage =>
      throw _privateConstructorUsedError; // Timestamps
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this RiderProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RiderProfileCopyWith<RiderProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RiderProfileCopyWith<$Res> {
  factory $RiderProfileCopyWith(
          RiderProfile value, $Res Function(RiderProfile) then) =
      _$RiderProfileCopyWithImpl<$Res, RiderProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      List<EmergencyContact> emergencyContacts,
      bool allowSharedRides,
      bool allowPetFriendlyRides,
      bool preferFemaleDrivers,
      bool accessibilityNeeds,
      String? accessibilityDetails,
      List<FavoriteLocation> favoriteLocations,
      String? preferredPaymentMethod,
      int totalRides,
      double averageRating,
      int totalSpent,
      bool isProfileComplete,
      double completionPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$RiderProfileCopyWithImpl<$Res, $Val extends RiderProfile>
    implements $RiderProfileCopyWith<$Res> {
  _$RiderProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContacts = null,
    Object? allowSharedRides = null,
    Object? allowPetFriendlyRides = null,
    Object? preferFemaleDrivers = null,
    Object? accessibilityNeeds = null,
    Object? accessibilityDetails = freezed,
    Object? favoriteLocations = null,
    Object? preferredPaymentMethod = freezed,
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalSpent = null,
    Object? isProfileComplete = null,
    Object? completionPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContacts: null == emergencyContacts
          ? _value.emergencyContacts
          : emergencyContacts // ignore: cast_nullable_to_non_nullable
              as List<EmergencyContact>,
      allowSharedRides: null == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      allowPetFriendlyRides: null == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      preferFemaleDrivers: null == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityNeeds: null == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      favoriteLocations: null == favoriteLocations
          ? _value.favoriteLocations
          : favoriteLocations // ignore: cast_nullable_to_non_nullable
              as List<FavoriteLocation>,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      averageRating: null == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as int,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RiderProfileImplCopyWith<$Res>
    implements $RiderProfileCopyWith<$Res> {
  factory _$$RiderProfileImplCopyWith(
          _$RiderProfileImpl value, $Res Function(_$RiderProfileImpl) then) =
      __$$RiderProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      List<EmergencyContact> emergencyContacts,
      bool allowSharedRides,
      bool allowPetFriendlyRides,
      bool preferFemaleDrivers,
      bool accessibilityNeeds,
      String? accessibilityDetails,
      List<FavoriteLocation> favoriteLocations,
      String? preferredPaymentMethod,
      int totalRides,
      double averageRating,
      int totalSpent,
      bool isProfileComplete,
      double completionPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$RiderProfileImplCopyWithImpl<$Res>
    extends _$RiderProfileCopyWithImpl<$Res, _$RiderProfileImpl>
    implements _$$RiderProfileImplCopyWith<$Res> {
  __$$RiderProfileImplCopyWithImpl(
      _$RiderProfileImpl _value, $Res Function(_$RiderProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? emergencyContacts = null,
    Object? allowSharedRides = null,
    Object? allowPetFriendlyRides = null,
    Object? preferFemaleDrivers = null,
    Object? accessibilityNeeds = null,
    Object? accessibilityDetails = freezed,
    Object? favoriteLocations = null,
    Object? preferredPaymentMethod = freezed,
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalSpent = null,
    Object? isProfileComplete = null,
    Object? completionPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$RiderProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContacts: null == emergencyContacts
          ? _value._emergencyContacts
          : emergencyContacts // ignore: cast_nullable_to_non_nullable
              as List<EmergencyContact>,
      allowSharedRides: null == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      allowPetFriendlyRides: null == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      preferFemaleDrivers: null == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityNeeds: null == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      favoriteLocations: null == favoriteLocations
          ? _value._favoriteLocations
          : favoriteLocations // ignore: cast_nullable_to_non_nullable
              as List<FavoriteLocation>,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      averageRating: null == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double,
      totalSpent: null == totalSpent
          ? _value.totalSpent
          : totalSpent // ignore: cast_nullable_to_non_nullable
              as int,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RiderProfileImpl implements _RiderProfile {
  const _$RiderProfileImpl(
      {required this.id,
      required this.userId,
      final List<EmergencyContact> emergencyContacts = const [],
      this.allowSharedRides = false,
      this.allowPetFriendlyRides = false,
      this.preferFemaleDrivers = false,
      this.accessibilityNeeds = false,
      this.accessibilityDetails,
      final List<FavoriteLocation> favoriteLocations = const [],
      this.preferredPaymentMethod,
      this.totalRides = 0,
      this.averageRating = 0.0,
      this.totalSpent = 0,
      this.isProfileComplete = false,
      this.completionPercentage = 0.0,
      this.createdAt,
      this.updatedAt})
      : _emergencyContacts = emergencyContacts,
        _favoriteLocations = favoriteLocations;

  factory _$RiderProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$RiderProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
// Emergency contacts
  final List<EmergencyContact> _emergencyContacts;
// Emergency contacts
  @override
  @JsonKey()
  List<EmergencyContact> get emergencyContacts {
    if (_emergencyContacts is EqualUnmodifiableListView)
      return _emergencyContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_emergencyContacts);
  }

// Preferences
  @override
  @JsonKey()
  final bool allowSharedRides;
  @override
  @JsonKey()
  final bool allowPetFriendlyRides;
  @override
  @JsonKey()
  final bool preferFemaleDrivers;
  @override
  @JsonKey()
  final bool accessibilityNeeds;
// Accessibility details
  @override
  final String? accessibilityDetails;
// Favorite locations
  final List<FavoriteLocation> _favoriteLocations;
// Favorite locations
  @override
  @JsonKey()
  List<FavoriteLocation> get favoriteLocations {
    if (_favoriteLocations is EqualUnmodifiableListView)
      return _favoriteLocations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_favoriteLocations);
  }

// Payment preferences
  @override
  final String? preferredPaymentMethod;
// Ride history stats
  @override
  @JsonKey()
  final int totalRides;
  @override
  @JsonKey()
  final double averageRating;
  @override
  @JsonKey()
  final int totalSpent;
// Profile completion
  @override
  @JsonKey()
  final bool isProfileComplete;
  @override
  @JsonKey()
  final double completionPercentage;
// Timestamps
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'RiderProfile(id: $id, userId: $userId, emergencyContacts: $emergencyContacts, allowSharedRides: $allowSharedRides, allowPetFriendlyRides: $allowPetFriendlyRides, preferFemaleDrivers: $preferFemaleDrivers, accessibilityNeeds: $accessibilityNeeds, accessibilityDetails: $accessibilityDetails, favoriteLocations: $favoriteLocations, preferredPaymentMethod: $preferredPaymentMethod, totalRides: $totalRides, averageRating: $averageRating, totalSpent: $totalSpent, isProfileComplete: $isProfileComplete, completionPercentage: $completionPercentage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RiderProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality()
                .equals(other._emergencyContacts, _emergencyContacts) &&
            (identical(other.allowSharedRides, allowSharedRides) ||
                other.allowSharedRides == allowSharedRides) &&
            (identical(other.allowPetFriendlyRides, allowPetFriendlyRides) ||
                other.allowPetFriendlyRides == allowPetFriendlyRides) &&
            (identical(other.preferFemaleDrivers, preferFemaleDrivers) ||
                other.preferFemaleDrivers == preferFemaleDrivers) &&
            (identical(other.accessibilityNeeds, accessibilityNeeds) ||
                other.accessibilityNeeds == accessibilityNeeds) &&
            (identical(other.accessibilityDetails, accessibilityDetails) ||
                other.accessibilityDetails == accessibilityDetails) &&
            const DeepCollectionEquality()
                .equals(other._favoriteLocations, _favoriteLocations) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.averageRating, averageRating) ||
                other.averageRating == averageRating) &&
            (identical(other.totalSpent, totalSpent) ||
                other.totalSpent == totalSpent) &&
            (identical(other.isProfileComplete, isProfileComplete) ||
                other.isProfileComplete == isProfileComplete) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      const DeepCollectionEquality().hash(_emergencyContacts),
      allowSharedRides,
      allowPetFriendlyRides,
      preferFemaleDrivers,
      accessibilityNeeds,
      accessibilityDetails,
      const DeepCollectionEquality().hash(_favoriteLocations),
      preferredPaymentMethod,
      totalRides,
      averageRating,
      totalSpent,
      isProfileComplete,
      completionPercentage,
      createdAt,
      updatedAt);

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      __$$RiderProfileImplCopyWithImpl<_$RiderProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RiderProfileImplToJson(
      this,
    );
  }
}

abstract class _RiderProfile implements RiderProfile {
  const factory _RiderProfile(
      {required final String id,
      required final String userId,
      final List<EmergencyContact> emergencyContacts,
      final bool allowSharedRides,
      final bool allowPetFriendlyRides,
      final bool preferFemaleDrivers,
      final bool accessibilityNeeds,
      final String? accessibilityDetails,
      final List<FavoriteLocation> favoriteLocations,
      final String? preferredPaymentMethod,
      final int totalRides,
      final double averageRating,
      final int totalSpent,
      final bool isProfileComplete,
      final double completionPercentage,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$RiderProfileImpl;

  factory _RiderProfile.fromJson(Map<String, dynamic> json) =
      _$RiderProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId; // Emergency contacts
  @override
  List<EmergencyContact> get emergencyContacts; // Preferences
  @override
  bool get allowSharedRides;
  @override
  bool get allowPetFriendlyRides;
  @override
  bool get preferFemaleDrivers;
  @override
  bool get accessibilityNeeds; // Accessibility details
  @override
  String? get accessibilityDetails; // Favorite locations
  @override
  List<FavoriteLocation> get favoriteLocations; // Payment preferences
  @override
  String? get preferredPaymentMethod; // Ride history stats
  @override
  int get totalRides;
  @override
  double get averageRating;
  @override
  int get totalSpent; // Profile completion
  @override
  bool get isProfileComplete;
  @override
  double get completionPercentage; // Timestamps
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of RiderProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RiderProfileImplCopyWith<_$RiderProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

EmergencyContact _$EmergencyContactFromJson(Map<String, dynamic> json) {
  return _EmergencyContact.fromJson(json);
}

/// @nodoc
mixin _$EmergencyContact {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get relationship => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this EmergencyContact to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of EmergencyContact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmergencyContactCopyWith<EmergencyContact> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmergencyContactCopyWith<$Res> {
  factory $EmergencyContactCopyWith(
          EmergencyContact value, $Res Function(EmergencyContact) then) =
      _$EmergencyContactCopyWithImpl<$Res, EmergencyContact>;
  @useResult
  $Res call(
      {String id,
      String name,
      String phoneNumber,
      String relationship,
      String? email,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$EmergencyContactCopyWithImpl<$Res, $Val extends EmergencyContact>
    implements $EmergencyContactCopyWith<$Res> {
  _$EmergencyContactCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmergencyContact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = null,
    Object? relationship = null,
    Object? email = freezed,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      relationship: null == relationship
          ? _value.relationship
          : relationship // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EmergencyContactImplCopyWith<$Res>
    implements $EmergencyContactCopyWith<$Res> {
  factory _$$EmergencyContactImplCopyWith(_$EmergencyContactImpl value,
          $Res Function(_$EmergencyContactImpl) then) =
      __$$EmergencyContactImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String phoneNumber,
      String relationship,
      String? email,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$EmergencyContactImplCopyWithImpl<$Res>
    extends _$EmergencyContactCopyWithImpl<$Res, _$EmergencyContactImpl>
    implements _$$EmergencyContactImplCopyWith<$Res> {
  __$$EmergencyContactImplCopyWithImpl(_$EmergencyContactImpl _value,
      $Res Function(_$EmergencyContactImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmergencyContact
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? phoneNumber = null,
    Object? relationship = null,
    Object? email = freezed,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$EmergencyContactImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      relationship: null == relationship
          ? _value.relationship
          : relationship // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EmergencyContactImpl implements _EmergencyContact {
  const _$EmergencyContactImpl(
      {required this.id,
      required this.name,
      required this.phoneNumber,
      required this.relationship,
      this.email,
      this.isActive = true,
      this.createdAt,
      this.updatedAt});

  factory _$EmergencyContactImpl.fromJson(Map<String, dynamic> json) =>
      _$$EmergencyContactImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String phoneNumber;
  @override
  final String relationship;
  @override
  final String? email;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'EmergencyContact(id: $id, name: $name, phoneNumber: $phoneNumber, relationship: $relationship, email: $email, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmergencyContactImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.relationship, relationship) ||
                other.relationship == relationship) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, phoneNumber,
      relationship, email, isActive, createdAt, updatedAt);

  /// Create a copy of EmergencyContact
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EmergencyContactImplCopyWith<_$EmergencyContactImpl> get copyWith =>
      __$$EmergencyContactImplCopyWithImpl<_$EmergencyContactImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EmergencyContactImplToJson(
      this,
    );
  }
}

abstract class _EmergencyContact implements EmergencyContact {
  const factory _EmergencyContact(
      {required final String id,
      required final String name,
      required final String phoneNumber,
      required final String relationship,
      final String? email,
      final bool isActive,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$EmergencyContactImpl;

  factory _EmergencyContact.fromJson(Map<String, dynamic> json) =
      _$EmergencyContactImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get phoneNumber;
  @override
  String get relationship;
  @override
  String? get email;
  @override
  bool get isActive;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of EmergencyContact
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EmergencyContactImplCopyWith<_$EmergencyContactImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FavoriteLocation _$FavoriteLocationFromJson(Map<String, dynamic> json) {
  return _FavoriteLocation.fromJson(json);
}

/// @nodoc
mixin _$FavoriteLocation {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  LocationType get type => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this FavoriteLocation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FavoriteLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FavoriteLocationCopyWith<FavoriteLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FavoriteLocationCopyWith<$Res> {
  factory $FavoriteLocationCopyWith(
          FavoriteLocation value, $Res Function(FavoriteLocation) then) =
      _$FavoriteLocationCopyWithImpl<$Res, FavoriteLocation>;
  @useResult
  $Res call(
      {String id,
      String name,
      String address,
      double latitude,
      double longitude,
      String? description,
      LocationType type,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$FavoriteLocationCopyWithImpl<$Res, $Val extends FavoriteLocation>
    implements $FavoriteLocationCopyWith<$Res> {
  _$FavoriteLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FavoriteLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? description = freezed,
    Object? type = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as LocationType,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FavoriteLocationImplCopyWith<$Res>
    implements $FavoriteLocationCopyWith<$Res> {
  factory _$$FavoriteLocationImplCopyWith(_$FavoriteLocationImpl value,
          $Res Function(_$FavoriteLocationImpl) then) =
      __$$FavoriteLocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String address,
      double latitude,
      double longitude,
      String? description,
      LocationType type,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$FavoriteLocationImplCopyWithImpl<$Res>
    extends _$FavoriteLocationCopyWithImpl<$Res, _$FavoriteLocationImpl>
    implements _$$FavoriteLocationImplCopyWith<$Res> {
  __$$FavoriteLocationImplCopyWithImpl(_$FavoriteLocationImpl _value,
      $Res Function(_$FavoriteLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of FavoriteLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? description = freezed,
    Object? type = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$FavoriteLocationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as LocationType,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FavoriteLocationImpl implements _FavoriteLocation {
  const _$FavoriteLocationImpl(
      {required this.id,
      required this.name,
      required this.address,
      required this.latitude,
      required this.longitude,
      this.description,
      this.type = LocationType.other,
      this.isActive = true,
      this.createdAt,
      this.updatedAt});

  factory _$FavoriteLocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$FavoriteLocationImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String address;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String? description;
  @override
  @JsonKey()
  final LocationType type;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'FavoriteLocation(id: $id, name: $name, address: $address, latitude: $latitude, longitude: $longitude, description: $description, type: $type, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FavoriteLocationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, address, latitude,
      longitude, description, type, isActive, createdAt, updatedAt);

  /// Create a copy of FavoriteLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FavoriteLocationImplCopyWith<_$FavoriteLocationImpl> get copyWith =>
      __$$FavoriteLocationImplCopyWithImpl<_$FavoriteLocationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FavoriteLocationImplToJson(
      this,
    );
  }
}

abstract class _FavoriteLocation implements FavoriteLocation {
  const factory _FavoriteLocation(
      {required final String id,
      required final String name,
      required final String address,
      required final double latitude,
      required final double longitude,
      final String? description,
      final LocationType type,
      final bool isActive,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$FavoriteLocationImpl;

  factory _FavoriteLocation.fromJson(Map<String, dynamic> json) =
      _$FavoriteLocationImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get address;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  String? get description;
  @override
  LocationType get type;
  @override
  bool get isActive;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of FavoriteLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FavoriteLocationImplCopyWith<_$FavoriteLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateRiderProfileRequest _$CreateRiderProfileRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateRiderProfileRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateRiderProfileRequest {
  String get userId => throw _privateConstructorUsedError;
  List<CreateEmergencyContactRequest> get emergencyContacts =>
      throw _privateConstructorUsedError;
  bool get allowSharedRides => throw _privateConstructorUsedError;
  bool get allowPetFriendlyRides => throw _privateConstructorUsedError;
  bool get preferFemaleDrivers => throw _privateConstructorUsedError;
  bool get accessibilityNeeds => throw _privateConstructorUsedError;
  String? get accessibilityDetails => throw _privateConstructorUsedError;
  List<CreateFavoriteLocationRequest> get favoriteLocations =>
      throw _privateConstructorUsedError;
  String? get preferredPaymentMethod => throw _privateConstructorUsedError;

  /// Serializes this CreateRiderProfileRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateRiderProfileRequestCopyWith<CreateRiderProfileRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateRiderProfileRequestCopyWith<$Res> {
  factory $CreateRiderProfileRequestCopyWith(CreateRiderProfileRequest value,
          $Res Function(CreateRiderProfileRequest) then) =
      _$CreateRiderProfileRequestCopyWithImpl<$Res, CreateRiderProfileRequest>;
  @useResult
  $Res call(
      {String userId,
      List<CreateEmergencyContactRequest> emergencyContacts,
      bool allowSharedRides,
      bool allowPetFriendlyRides,
      bool preferFemaleDrivers,
      bool accessibilityNeeds,
      String? accessibilityDetails,
      List<CreateFavoriteLocationRequest> favoriteLocations,
      String? preferredPaymentMethod});
}

/// @nodoc
class _$CreateRiderProfileRequestCopyWithImpl<$Res,
        $Val extends CreateRiderProfileRequest>
    implements $CreateRiderProfileRequestCopyWith<$Res> {
  _$CreateRiderProfileRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? emergencyContacts = null,
    Object? allowSharedRides = null,
    Object? allowPetFriendlyRides = null,
    Object? preferFemaleDrivers = null,
    Object? accessibilityNeeds = null,
    Object? accessibilityDetails = freezed,
    Object? favoriteLocations = null,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContacts: null == emergencyContacts
          ? _value.emergencyContacts
          : emergencyContacts // ignore: cast_nullable_to_non_nullable
              as List<CreateEmergencyContactRequest>,
      allowSharedRides: null == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      allowPetFriendlyRides: null == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      preferFemaleDrivers: null == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityNeeds: null == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      favoriteLocations: null == favoriteLocations
          ? _value.favoriteLocations
          : favoriteLocations // ignore: cast_nullable_to_non_nullable
              as List<CreateFavoriteLocationRequest>,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateRiderProfileRequestImplCopyWith<$Res>
    implements $CreateRiderProfileRequestCopyWith<$Res> {
  factory _$$CreateRiderProfileRequestImplCopyWith(
          _$CreateRiderProfileRequestImpl value,
          $Res Function(_$CreateRiderProfileRequestImpl) then) =
      __$$CreateRiderProfileRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      List<CreateEmergencyContactRequest> emergencyContacts,
      bool allowSharedRides,
      bool allowPetFriendlyRides,
      bool preferFemaleDrivers,
      bool accessibilityNeeds,
      String? accessibilityDetails,
      List<CreateFavoriteLocationRequest> favoriteLocations,
      String? preferredPaymentMethod});
}

/// @nodoc
class __$$CreateRiderProfileRequestImplCopyWithImpl<$Res>
    extends _$CreateRiderProfileRequestCopyWithImpl<$Res,
        _$CreateRiderProfileRequestImpl>
    implements _$$CreateRiderProfileRequestImplCopyWith<$Res> {
  __$$CreateRiderProfileRequestImplCopyWithImpl(
      _$CreateRiderProfileRequestImpl _value,
      $Res Function(_$CreateRiderProfileRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? emergencyContacts = null,
    Object? allowSharedRides = null,
    Object? allowPetFriendlyRides = null,
    Object? preferFemaleDrivers = null,
    Object? accessibilityNeeds = null,
    Object? accessibilityDetails = freezed,
    Object? favoriteLocations = null,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_$CreateRiderProfileRequestImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      emergencyContacts: null == emergencyContacts
          ? _value._emergencyContacts
          : emergencyContacts // ignore: cast_nullable_to_non_nullable
              as List<CreateEmergencyContactRequest>,
      allowSharedRides: null == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      allowPetFriendlyRides: null == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      preferFemaleDrivers: null == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityNeeds: null == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      favoriteLocations: null == favoriteLocations
          ? _value._favoriteLocations
          : favoriteLocations // ignore: cast_nullable_to_non_nullable
              as List<CreateFavoriteLocationRequest>,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateRiderProfileRequestImpl implements _CreateRiderProfileRequest {
  const _$CreateRiderProfileRequestImpl(
      {required this.userId,
      final List<CreateEmergencyContactRequest> emergencyContacts = const [],
      this.allowSharedRides = false,
      this.allowPetFriendlyRides = false,
      this.preferFemaleDrivers = false,
      this.accessibilityNeeds = false,
      this.accessibilityDetails,
      final List<CreateFavoriteLocationRequest> favoriteLocations = const [],
      this.preferredPaymentMethod})
      : _emergencyContacts = emergencyContacts,
        _favoriteLocations = favoriteLocations;

  factory _$CreateRiderProfileRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateRiderProfileRequestImplFromJson(json);

  @override
  final String userId;
  final List<CreateEmergencyContactRequest> _emergencyContacts;
  @override
  @JsonKey()
  List<CreateEmergencyContactRequest> get emergencyContacts {
    if (_emergencyContacts is EqualUnmodifiableListView)
      return _emergencyContacts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_emergencyContacts);
  }

  @override
  @JsonKey()
  final bool allowSharedRides;
  @override
  @JsonKey()
  final bool allowPetFriendlyRides;
  @override
  @JsonKey()
  final bool preferFemaleDrivers;
  @override
  @JsonKey()
  final bool accessibilityNeeds;
  @override
  final String? accessibilityDetails;
  final List<CreateFavoriteLocationRequest> _favoriteLocations;
  @override
  @JsonKey()
  List<CreateFavoriteLocationRequest> get favoriteLocations {
    if (_favoriteLocations is EqualUnmodifiableListView)
      return _favoriteLocations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_favoriteLocations);
  }

  @override
  final String? preferredPaymentMethod;

  @override
  String toString() {
    return 'CreateRiderProfileRequest(userId: $userId, emergencyContacts: $emergencyContacts, allowSharedRides: $allowSharedRides, allowPetFriendlyRides: $allowPetFriendlyRides, preferFemaleDrivers: $preferFemaleDrivers, accessibilityNeeds: $accessibilityNeeds, accessibilityDetails: $accessibilityDetails, favoriteLocations: $favoriteLocations, preferredPaymentMethod: $preferredPaymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateRiderProfileRequestImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality()
                .equals(other._emergencyContacts, _emergencyContacts) &&
            (identical(other.allowSharedRides, allowSharedRides) ||
                other.allowSharedRides == allowSharedRides) &&
            (identical(other.allowPetFriendlyRides, allowPetFriendlyRides) ||
                other.allowPetFriendlyRides == allowPetFriendlyRides) &&
            (identical(other.preferFemaleDrivers, preferFemaleDrivers) ||
                other.preferFemaleDrivers == preferFemaleDrivers) &&
            (identical(other.accessibilityNeeds, accessibilityNeeds) ||
                other.accessibilityNeeds == accessibilityNeeds) &&
            (identical(other.accessibilityDetails, accessibilityDetails) ||
                other.accessibilityDetails == accessibilityDetails) &&
            const DeepCollectionEquality()
                .equals(other._favoriteLocations, _favoriteLocations) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      const DeepCollectionEquality().hash(_emergencyContacts),
      allowSharedRides,
      allowPetFriendlyRides,
      preferFemaleDrivers,
      accessibilityNeeds,
      accessibilityDetails,
      const DeepCollectionEquality().hash(_favoriteLocations),
      preferredPaymentMethod);

  /// Create a copy of CreateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateRiderProfileRequestImplCopyWith<_$CreateRiderProfileRequestImpl>
      get copyWith => __$$CreateRiderProfileRequestImplCopyWithImpl<
          _$CreateRiderProfileRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateRiderProfileRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateRiderProfileRequest implements CreateRiderProfileRequest {
  const factory _CreateRiderProfileRequest(
      {required final String userId,
      final List<CreateEmergencyContactRequest> emergencyContacts,
      final bool allowSharedRides,
      final bool allowPetFriendlyRides,
      final bool preferFemaleDrivers,
      final bool accessibilityNeeds,
      final String? accessibilityDetails,
      final List<CreateFavoriteLocationRequest> favoriteLocations,
      final String? preferredPaymentMethod}) = _$CreateRiderProfileRequestImpl;

  factory _CreateRiderProfileRequest.fromJson(Map<String, dynamic> json) =
      _$CreateRiderProfileRequestImpl.fromJson;

  @override
  String get userId;
  @override
  List<CreateEmergencyContactRequest> get emergencyContacts;
  @override
  bool get allowSharedRides;
  @override
  bool get allowPetFriendlyRides;
  @override
  bool get preferFemaleDrivers;
  @override
  bool get accessibilityNeeds;
  @override
  String? get accessibilityDetails;
  @override
  List<CreateFavoriteLocationRequest> get favoriteLocations;
  @override
  String? get preferredPaymentMethod;

  /// Create a copy of CreateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateRiderProfileRequestImplCopyWith<_$CreateRiderProfileRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreateEmergencyContactRequest _$CreateEmergencyContactRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateEmergencyContactRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateEmergencyContactRequest {
  String get name => throw _privateConstructorUsedError;
  String get phoneNumber => throw _privateConstructorUsedError;
  String get relationship => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;

  /// Serializes this CreateEmergencyContactRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateEmergencyContactRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateEmergencyContactRequestCopyWith<CreateEmergencyContactRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateEmergencyContactRequestCopyWith<$Res> {
  factory $CreateEmergencyContactRequestCopyWith(
          CreateEmergencyContactRequest value,
          $Res Function(CreateEmergencyContactRequest) then) =
      _$CreateEmergencyContactRequestCopyWithImpl<$Res,
          CreateEmergencyContactRequest>;
  @useResult
  $Res call(
      {String name, String phoneNumber, String relationship, String? email});
}

/// @nodoc
class _$CreateEmergencyContactRequestCopyWithImpl<$Res,
        $Val extends CreateEmergencyContactRequest>
    implements $CreateEmergencyContactRequestCopyWith<$Res> {
  _$CreateEmergencyContactRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateEmergencyContactRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? relationship = null,
    Object? email = freezed,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      relationship: null == relationship
          ? _value.relationship
          : relationship // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateEmergencyContactRequestImplCopyWith<$Res>
    implements $CreateEmergencyContactRequestCopyWith<$Res> {
  factory _$$CreateEmergencyContactRequestImplCopyWith(
          _$CreateEmergencyContactRequestImpl value,
          $Res Function(_$CreateEmergencyContactRequestImpl) then) =
      __$$CreateEmergencyContactRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name, String phoneNumber, String relationship, String? email});
}

/// @nodoc
class __$$CreateEmergencyContactRequestImplCopyWithImpl<$Res>
    extends _$CreateEmergencyContactRequestCopyWithImpl<$Res,
        _$CreateEmergencyContactRequestImpl>
    implements _$$CreateEmergencyContactRequestImplCopyWith<$Res> {
  __$$CreateEmergencyContactRequestImplCopyWithImpl(
      _$CreateEmergencyContactRequestImpl _value,
      $Res Function(_$CreateEmergencyContactRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateEmergencyContactRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? phoneNumber = null,
    Object? relationship = null,
    Object? email = freezed,
  }) {
    return _then(_$CreateEmergencyContactRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: null == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
      relationship: null == relationship
          ? _value.relationship
          : relationship // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateEmergencyContactRequestImpl
    implements _CreateEmergencyContactRequest {
  const _$CreateEmergencyContactRequestImpl(
      {required this.name,
      required this.phoneNumber,
      required this.relationship,
      this.email});

  factory _$CreateEmergencyContactRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CreateEmergencyContactRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String phoneNumber;
  @override
  final String relationship;
  @override
  final String? email;

  @override
  String toString() {
    return 'CreateEmergencyContactRequest(name: $name, phoneNumber: $phoneNumber, relationship: $relationship, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateEmergencyContactRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.relationship, relationship) ||
                other.relationship == relationship) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, name, phoneNumber, relationship, email);

  /// Create a copy of CreateEmergencyContactRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateEmergencyContactRequestImplCopyWith<
          _$CreateEmergencyContactRequestImpl>
      get copyWith => __$$CreateEmergencyContactRequestImplCopyWithImpl<
          _$CreateEmergencyContactRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateEmergencyContactRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateEmergencyContactRequest
    implements CreateEmergencyContactRequest {
  const factory _CreateEmergencyContactRequest(
      {required final String name,
      required final String phoneNumber,
      required final String relationship,
      final String? email}) = _$CreateEmergencyContactRequestImpl;

  factory _CreateEmergencyContactRequest.fromJson(Map<String, dynamic> json) =
      _$CreateEmergencyContactRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get phoneNumber;
  @override
  String get relationship;
  @override
  String? get email;

  /// Create a copy of CreateEmergencyContactRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateEmergencyContactRequestImplCopyWith<
          _$CreateEmergencyContactRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreateFavoriteLocationRequest _$CreateFavoriteLocationRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateFavoriteLocationRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateFavoriteLocationRequest {
  String get name => throw _privateConstructorUsedError;
  String get address => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  LocationType get type => throw _privateConstructorUsedError;

  /// Serializes this CreateFavoriteLocationRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateFavoriteLocationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateFavoriteLocationRequestCopyWith<CreateFavoriteLocationRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateFavoriteLocationRequestCopyWith<$Res> {
  factory $CreateFavoriteLocationRequestCopyWith(
          CreateFavoriteLocationRequest value,
          $Res Function(CreateFavoriteLocationRequest) then) =
      _$CreateFavoriteLocationRequestCopyWithImpl<$Res,
          CreateFavoriteLocationRequest>;
  @useResult
  $Res call(
      {String name,
      String address,
      double latitude,
      double longitude,
      String? description,
      LocationType type});
}

/// @nodoc
class _$CreateFavoriteLocationRequestCopyWithImpl<$Res,
        $Val extends CreateFavoriteLocationRequest>
    implements $CreateFavoriteLocationRequestCopyWith<$Res> {
  _$CreateFavoriteLocationRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateFavoriteLocationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? description = freezed,
    Object? type = null,
  }) {
    return _then(_value.copyWith(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as LocationType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateFavoriteLocationRequestImplCopyWith<$Res>
    implements $CreateFavoriteLocationRequestCopyWith<$Res> {
  factory _$$CreateFavoriteLocationRequestImplCopyWith(
          _$CreateFavoriteLocationRequestImpl value,
          $Res Function(_$CreateFavoriteLocationRequestImpl) then) =
      __$$CreateFavoriteLocationRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String name,
      String address,
      double latitude,
      double longitude,
      String? description,
      LocationType type});
}

/// @nodoc
class __$$CreateFavoriteLocationRequestImplCopyWithImpl<$Res>
    extends _$CreateFavoriteLocationRequestCopyWithImpl<$Res,
        _$CreateFavoriteLocationRequestImpl>
    implements _$$CreateFavoriteLocationRequestImplCopyWith<$Res> {
  __$$CreateFavoriteLocationRequestImplCopyWithImpl(
      _$CreateFavoriteLocationRequestImpl _value,
      $Res Function(_$CreateFavoriteLocationRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateFavoriteLocationRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
    Object? address = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? description = freezed,
    Object? type = null,
  }) {
    return _then(_$CreateFavoriteLocationRequestImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      address: null == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as LocationType,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateFavoriteLocationRequestImpl
    implements _CreateFavoriteLocationRequest {
  const _$CreateFavoriteLocationRequestImpl(
      {required this.name,
      required this.address,
      required this.latitude,
      required this.longitude,
      this.description,
      this.type = LocationType.other});

  factory _$CreateFavoriteLocationRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CreateFavoriteLocationRequestImplFromJson(json);

  @override
  final String name;
  @override
  final String address;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String? description;
  @override
  @JsonKey()
  final LocationType type;

  @override
  String toString() {
    return 'CreateFavoriteLocationRequest(name: $name, address: $address, latitude: $latitude, longitude: $longitude, description: $description, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateFavoriteLocationRequestImpl &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, name, address, latitude, longitude, description, type);

  /// Create a copy of CreateFavoriteLocationRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateFavoriteLocationRequestImplCopyWith<
          _$CreateFavoriteLocationRequestImpl>
      get copyWith => __$$CreateFavoriteLocationRequestImplCopyWithImpl<
          _$CreateFavoriteLocationRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateFavoriteLocationRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateFavoriteLocationRequest
    implements CreateFavoriteLocationRequest {
  const factory _CreateFavoriteLocationRequest(
      {required final String name,
      required final String address,
      required final double latitude,
      required final double longitude,
      final String? description,
      final LocationType type}) = _$CreateFavoriteLocationRequestImpl;

  factory _CreateFavoriteLocationRequest.fromJson(Map<String, dynamic> json) =
      _$CreateFavoriteLocationRequestImpl.fromJson;

  @override
  String get name;
  @override
  String get address;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  String? get description;
  @override
  LocationType get type;

  /// Create a copy of CreateFavoriteLocationRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateFavoriteLocationRequestImplCopyWith<
          _$CreateFavoriteLocationRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpdateRiderProfileRequest _$UpdateRiderProfileRequestFromJson(
    Map<String, dynamic> json) {
  return _UpdateRiderProfileRequest.fromJson(json);
}

/// @nodoc
mixin _$UpdateRiderProfileRequest {
  bool? get allowSharedRides => throw _privateConstructorUsedError;
  bool? get allowPetFriendlyRides => throw _privateConstructorUsedError;
  bool? get preferFemaleDrivers => throw _privateConstructorUsedError;
  bool? get accessibilityNeeds => throw _privateConstructorUsedError;
  String? get accessibilityDetails => throw _privateConstructorUsedError;
  String? get preferredPaymentMethod => throw _privateConstructorUsedError;

  /// Serializes this UpdateRiderProfileRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateRiderProfileRequestCopyWith<UpdateRiderProfileRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateRiderProfileRequestCopyWith<$Res> {
  factory $UpdateRiderProfileRequestCopyWith(UpdateRiderProfileRequest value,
          $Res Function(UpdateRiderProfileRequest) then) =
      _$UpdateRiderProfileRequestCopyWithImpl<$Res, UpdateRiderProfileRequest>;
  @useResult
  $Res call(
      {bool? allowSharedRides,
      bool? allowPetFriendlyRides,
      bool? preferFemaleDrivers,
      bool? accessibilityNeeds,
      String? accessibilityDetails,
      String? preferredPaymentMethod});
}

/// @nodoc
class _$UpdateRiderProfileRequestCopyWithImpl<$Res,
        $Val extends UpdateRiderProfileRequest>
    implements $UpdateRiderProfileRequestCopyWith<$Res> {
  _$UpdateRiderProfileRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowSharedRides = freezed,
    Object? allowPetFriendlyRides = freezed,
    Object? preferFemaleDrivers = freezed,
    Object? accessibilityNeeds = freezed,
    Object? accessibilityDetails = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_value.copyWith(
      allowSharedRides: freezed == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool?,
      allowPetFriendlyRides: freezed == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool?,
      preferFemaleDrivers: freezed == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool?,
      accessibilityNeeds: freezed == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool?,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateRiderProfileRequestImplCopyWith<$Res>
    implements $UpdateRiderProfileRequestCopyWith<$Res> {
  factory _$$UpdateRiderProfileRequestImplCopyWith(
          _$UpdateRiderProfileRequestImpl value,
          $Res Function(_$UpdateRiderProfileRequestImpl) then) =
      __$$UpdateRiderProfileRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool? allowSharedRides,
      bool? allowPetFriendlyRides,
      bool? preferFemaleDrivers,
      bool? accessibilityNeeds,
      String? accessibilityDetails,
      String? preferredPaymentMethod});
}

/// @nodoc
class __$$UpdateRiderProfileRequestImplCopyWithImpl<$Res>
    extends _$UpdateRiderProfileRequestCopyWithImpl<$Res,
        _$UpdateRiderProfileRequestImpl>
    implements _$$UpdateRiderProfileRequestImplCopyWith<$Res> {
  __$$UpdateRiderProfileRequestImplCopyWithImpl(
      _$UpdateRiderProfileRequestImpl _value,
      $Res Function(_$UpdateRiderProfileRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowSharedRides = freezed,
    Object? allowPetFriendlyRides = freezed,
    Object? preferFemaleDrivers = freezed,
    Object? accessibilityNeeds = freezed,
    Object? accessibilityDetails = freezed,
    Object? preferredPaymentMethod = freezed,
  }) {
    return _then(_$UpdateRiderProfileRequestImpl(
      allowSharedRides: freezed == allowSharedRides
          ? _value.allowSharedRides
          : allowSharedRides // ignore: cast_nullable_to_non_nullable
              as bool?,
      allowPetFriendlyRides: freezed == allowPetFriendlyRides
          ? _value.allowPetFriendlyRides
          : allowPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool?,
      preferFemaleDrivers: freezed == preferFemaleDrivers
          ? _value.preferFemaleDrivers
          : preferFemaleDrivers // ignore: cast_nullable_to_non_nullable
              as bool?,
      accessibilityNeeds: freezed == accessibilityNeeds
          ? _value.accessibilityNeeds
          : accessibilityNeeds // ignore: cast_nullable_to_non_nullable
              as bool?,
      accessibilityDetails: freezed == accessibilityDetails
          ? _value.accessibilityDetails
          : accessibilityDetails // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredPaymentMethod: freezed == preferredPaymentMethod
          ? _value.preferredPaymentMethod
          : preferredPaymentMethod // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateRiderProfileRequestImpl implements _UpdateRiderProfileRequest {
  const _$UpdateRiderProfileRequestImpl(
      {this.allowSharedRides,
      this.allowPetFriendlyRides,
      this.preferFemaleDrivers,
      this.accessibilityNeeds,
      this.accessibilityDetails,
      this.preferredPaymentMethod});

  factory _$UpdateRiderProfileRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateRiderProfileRequestImplFromJson(json);

  @override
  final bool? allowSharedRides;
  @override
  final bool? allowPetFriendlyRides;
  @override
  final bool? preferFemaleDrivers;
  @override
  final bool? accessibilityNeeds;
  @override
  final String? accessibilityDetails;
  @override
  final String? preferredPaymentMethod;

  @override
  String toString() {
    return 'UpdateRiderProfileRequest(allowSharedRides: $allowSharedRides, allowPetFriendlyRides: $allowPetFriendlyRides, preferFemaleDrivers: $preferFemaleDrivers, accessibilityNeeds: $accessibilityNeeds, accessibilityDetails: $accessibilityDetails, preferredPaymentMethod: $preferredPaymentMethod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateRiderProfileRequestImpl &&
            (identical(other.allowSharedRides, allowSharedRides) ||
                other.allowSharedRides == allowSharedRides) &&
            (identical(other.allowPetFriendlyRides, allowPetFriendlyRides) ||
                other.allowPetFriendlyRides == allowPetFriendlyRides) &&
            (identical(other.preferFemaleDrivers, preferFemaleDrivers) ||
                other.preferFemaleDrivers == preferFemaleDrivers) &&
            (identical(other.accessibilityNeeds, accessibilityNeeds) ||
                other.accessibilityNeeds == accessibilityNeeds) &&
            (identical(other.accessibilityDetails, accessibilityDetails) ||
                other.accessibilityDetails == accessibilityDetails) &&
            (identical(other.preferredPaymentMethod, preferredPaymentMethod) ||
                other.preferredPaymentMethod == preferredPaymentMethod));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      allowSharedRides,
      allowPetFriendlyRides,
      preferFemaleDrivers,
      accessibilityNeeds,
      accessibilityDetails,
      preferredPaymentMethod);

  /// Create a copy of UpdateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateRiderProfileRequestImplCopyWith<_$UpdateRiderProfileRequestImpl>
      get copyWith => __$$UpdateRiderProfileRequestImplCopyWithImpl<
          _$UpdateRiderProfileRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateRiderProfileRequestImplToJson(
      this,
    );
  }
}

abstract class _UpdateRiderProfileRequest implements UpdateRiderProfileRequest {
  const factory _UpdateRiderProfileRequest(
      {final bool? allowSharedRides,
      final bool? allowPetFriendlyRides,
      final bool? preferFemaleDrivers,
      final bool? accessibilityNeeds,
      final String? accessibilityDetails,
      final String? preferredPaymentMethod}) = _$UpdateRiderProfileRequestImpl;

  factory _UpdateRiderProfileRequest.fromJson(Map<String, dynamic> json) =
      _$UpdateRiderProfileRequestImpl.fromJson;

  @override
  bool? get allowSharedRides;
  @override
  bool? get allowPetFriendlyRides;
  @override
  bool? get preferFemaleDrivers;
  @override
  bool? get accessibilityNeeds;
  @override
  String? get accessibilityDetails;
  @override
  String? get preferredPaymentMethod;

  /// Create a copy of UpdateRiderProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateRiderProfileRequestImplCopyWith<_$UpdateRiderProfileRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
