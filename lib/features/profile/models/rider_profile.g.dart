// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rider_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RiderProfileImpl _$$RiderProfileImplFromJson(Map<String, dynamic> json) =>
    _$RiderProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      emergencyContacts: (json['emergencyContacts'] as List<dynamic>?)
              ?.map((e) => EmergencyContact.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      allowSharedRides: json['allowSharedRides'] as bool? ?? false,
      allowPetFriendlyRides: json['allowPetFriendlyRides'] as bool? ?? false,
      preferFemaleDrivers: json['preferFemaleDrivers'] as bool? ?? false,
      accessibilityNeeds: json['accessibilityNeeds'] as bool? ?? false,
      accessibilityDetails: json['accessibilityDetails'] as String?,
      favoriteLocations: (json['favoriteLocations'] as List<dynamic>?)
              ?.map((e) => FavoriteLocation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
      totalRides: (json['totalRides'] as num?)?.toInt() ?? 0,
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
      totalSpent: (json['totalSpent'] as num?)?.toInt() ?? 0,
      isProfileComplete: json['isProfileComplete'] as bool? ?? false,
      completionPercentage:
          (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$RiderProfileImplToJson(_$RiderProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'emergencyContacts': instance.emergencyContacts,
      'allowSharedRides': instance.allowSharedRides,
      'allowPetFriendlyRides': instance.allowPetFriendlyRides,
      'preferFemaleDrivers': instance.preferFemaleDrivers,
      'accessibilityNeeds': instance.accessibilityNeeds,
      'accessibilityDetails': instance.accessibilityDetails,
      'favoriteLocations': instance.favoriteLocations,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
      'totalRides': instance.totalRides,
      'averageRating': instance.averageRating,
      'totalSpent': instance.totalSpent,
      'isProfileComplete': instance.isProfileComplete,
      'completionPercentage': instance.completionPercentage,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$EmergencyContactImpl _$$EmergencyContactImplFromJson(
        Map<String, dynamic> json) =>
    _$EmergencyContactImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String,
      relationship: json['relationship'] as String,
      email: json['email'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$EmergencyContactImplToJson(
        _$EmergencyContactImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      'relationship': instance.relationship,
      'email': instance.email,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

_$FavoriteLocationImpl _$$FavoriteLocationImplFromJson(
        Map<String, dynamic> json) =>
    _$FavoriteLocationImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      description: json['description'] as String?,
      type: $enumDecodeNullable(_$LocationTypeEnumMap, json['type']) ??
          LocationType.other,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$FavoriteLocationImplToJson(
        _$FavoriteLocationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'description': instance.description,
      'type': _$LocationTypeEnumMap[instance.type]!,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$LocationTypeEnumMap = {
  LocationType.home: 'home',
  LocationType.work: 'work',
  LocationType.school: 'school',
  LocationType.gym: 'gym',
  LocationType.shopping: 'shopping',
  LocationType.restaurant: 'restaurant',
  LocationType.other: 'other',
};

_$CreateRiderProfileRequestImpl _$$CreateRiderProfileRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateRiderProfileRequestImpl(
      userId: json['userId'] as String,
      emergencyContacts: (json['emergencyContacts'] as List<dynamic>?)
              ?.map((e) => CreateEmergencyContactRequest.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      allowSharedRides: json['allowSharedRides'] as bool? ?? false,
      allowPetFriendlyRides: json['allowPetFriendlyRides'] as bool? ?? false,
      preferFemaleDrivers: json['preferFemaleDrivers'] as bool? ?? false,
      accessibilityNeeds: json['accessibilityNeeds'] as bool? ?? false,
      accessibilityDetails: json['accessibilityDetails'] as String?,
      favoriteLocations: (json['favoriteLocations'] as List<dynamic>?)
              ?.map((e) => CreateFavoriteLocationRequest.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
    );

Map<String, dynamic> _$$CreateRiderProfileRequestImplToJson(
        _$CreateRiderProfileRequestImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'emergencyContacts': instance.emergencyContacts,
      'allowSharedRides': instance.allowSharedRides,
      'allowPetFriendlyRides': instance.allowPetFriendlyRides,
      'preferFemaleDrivers': instance.preferFemaleDrivers,
      'accessibilityNeeds': instance.accessibilityNeeds,
      'accessibilityDetails': instance.accessibilityDetails,
      'favoriteLocations': instance.favoriteLocations,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
    };

_$CreateEmergencyContactRequestImpl
    _$$CreateEmergencyContactRequestImplFromJson(Map<String, dynamic> json) =>
        _$CreateEmergencyContactRequestImpl(
          name: json['name'] as String,
          phoneNumber: json['phoneNumber'] as String,
          relationship: json['relationship'] as String,
          email: json['email'] as String?,
        );

Map<String, dynamic> _$$CreateEmergencyContactRequestImplToJson(
        _$CreateEmergencyContactRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'phoneNumber': instance.phoneNumber,
      'relationship': instance.relationship,
      'email': instance.email,
    };

_$CreateFavoriteLocationRequestImpl
    _$$CreateFavoriteLocationRequestImplFromJson(Map<String, dynamic> json) =>
        _$CreateFavoriteLocationRequestImpl(
          name: json['name'] as String,
          address: json['address'] as String,
          latitude: (json['latitude'] as num).toDouble(),
          longitude: (json['longitude'] as num).toDouble(),
          description: json['description'] as String?,
          type: $enumDecodeNullable(_$LocationTypeEnumMap, json['type']) ??
              LocationType.other,
        );

Map<String, dynamic> _$$CreateFavoriteLocationRequestImplToJson(
        _$CreateFavoriteLocationRequestImpl instance) =>
    <String, dynamic>{
      'name': instance.name,
      'address': instance.address,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'description': instance.description,
      'type': _$LocationTypeEnumMap[instance.type]!,
    };

_$UpdateRiderProfileRequestImpl _$$UpdateRiderProfileRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$UpdateRiderProfileRequestImpl(
      allowSharedRides: json['allowSharedRides'] as bool?,
      allowPetFriendlyRides: json['allowPetFriendlyRides'] as bool?,
      preferFemaleDrivers: json['preferFemaleDrivers'] as bool?,
      accessibilityNeeds: json['accessibilityNeeds'] as bool?,
      accessibilityDetails: json['accessibilityDetails'] as String?,
      preferredPaymentMethod: json['preferredPaymentMethod'] as String?,
    );

Map<String, dynamic> _$$UpdateRiderProfileRequestImplToJson(
        _$UpdateRiderProfileRequestImpl instance) =>
    <String, dynamic>{
      'allowSharedRides': instance.allowSharedRides,
      'allowPetFriendlyRides': instance.allowPetFriendlyRides,
      'preferFemaleDrivers': instance.preferFemaleDrivers,
      'accessibilityNeeds': instance.accessibilityNeeds,
      'accessibilityDetails': instance.accessibilityDetails,
      'preferredPaymentMethod': instance.preferredPaymentMethod,
    };
