import 'package:freezed_annotation/freezed_annotation.dart';

part 'driver_profile.freezed.dart';
part 'driver_profile.g.dart';

/// Driver-specific profile model
@freezed
class DriverProfile with _$DriverProfile {
  const factory DriverProfile({
    required String id,
    required String userId,
    
    // License information
    required String licenseNumber,
    required DateTime licenseExpiryDate,
    required String licenseClass,
    String? licenseImageUrl,
    @Default(LicenseStatus.pending) LicenseStatus licenseStatus,
    
    // Vehicle information
    @Default([]) List<Vehicle> vehicles,
    String? activeVehicleId,
    
    // Driver preferences
    @Default(false) bool acceptSharedRides,
    @Default(false) bool acceptPetFriendlyRides,
    @Default(false) bool acceptLongDistanceRides,
    @Default(false) bool acceptCashPayments,
    
    // Availability
    @Default(false) bool isAvailable,
    @Default(false) bool isOnline,
    String? currentLocationLatitude,
    String? currentLocationLongitude,
    
    // Working hours
    @Default([]) List<WorkingHours> workingHours,
    
    // Driver stats
    @Default(0) int totalRides,
    @Default(0.0) double averageRating,
    @Default(0) int totalEarnings,
    @Default(0) int completedRides,
    @Default(0) int cancelledRides,
    
    // Verification status
    @Default(VerificationStatus.pending) VerificationStatus verificationStatus,
    @Default([]) List<String> verificationDocuments,
    
    // Profile completion
    @Default(false) bool isProfileComplete,
    @Default(0.0) double completionPercentage,
    
    // Timestamps
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _DriverProfile;

  factory DriverProfile.fromJson(Map<String, dynamic> json) =>
      _$DriverProfileFromJson(json);
}

/// Vehicle model
@freezed
class Vehicle with _$Vehicle {
  const factory Vehicle({
    required String id,
    required String make,
    required String model,
    required int year,
    required String color,
    required String licensePlate,
    required VehicleType type,
    required int seatingCapacity,
    String? imageUrl,
    String? registrationNumber,
    DateTime? registrationExpiry,
    String? insurancePolicyNumber,
    DateTime? insuranceExpiry,
    @Default(VehicleStatus.active) VehicleStatus status,
    @Default(true) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _Vehicle;

  factory Vehicle.fromJson(Map<String, dynamic> json) =>
      _$VehicleFromJson(json);
}

/// Working hours model
@freezed
class WorkingHours with _$WorkingHours {
  const factory WorkingHours({
    required String id,
    required DayOfWeek dayOfWeek,
    required String startTime, // Format: "HH:mm"
    required String endTime,   // Format: "HH:mm"
    @Default(true) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _WorkingHours;

  factory WorkingHours.fromJson(Map<String, dynamic> json) =>
      _$WorkingHoursFromJson(json);
}

/// License status enumeration
enum LicenseStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('verified')
  verified,
  @JsonValue('rejected')
  rejected,
  @JsonValue('expired')
  expired,
}

/// Vehicle type enumeration
enum VehicleType {
  @JsonValue('sedan')
  sedan,
  @JsonValue('suv')
  suv,
  @JsonValue('hatchback')
  hatchback,
  @JsonValue('pickup')
  pickup,
  @JsonValue('van')
  van,
  @JsonValue('motorcycle')
  motorcycle,
  @JsonValue('other')
  other,
}

/// Vehicle status enumeration
enum VehicleStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('maintenance')
  maintenance,
  @JsonValue('suspended')
  suspended,
}

/// Verification status enumeration
enum VerificationStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_review')
  inReview,
  @JsonValue('verified')
  verified,
  @JsonValue('rejected')
  rejected,
  @JsonValue('suspended')
  suspended,
}

/// Day of week enumeration
enum DayOfWeek {
  @JsonValue('monday')
  monday,
  @JsonValue('tuesday')
  tuesday,
  @JsonValue('wednesday')
  wednesday,
  @JsonValue('thursday')
  thursday,
  @JsonValue('friday')
  friday,
  @JsonValue('saturday')
  saturday,
  @JsonValue('sunday')
  sunday,
}

/// Extensions for enums
extension LicenseStatusExtension on LicenseStatus {
  String get displayName {
    switch (this) {
      case LicenseStatus.pending:
        return 'Pending Verification';
      case LicenseStatus.verified:
        return 'Verified';
      case LicenseStatus.rejected:
        return 'Rejected';
      case LicenseStatus.expired:
        return 'Expired';
    }
  }
}

extension VehicleTypeExtension on VehicleType {
  String get displayName {
    switch (this) {
      case VehicleType.sedan:
        return 'Sedan';
      case VehicleType.suv:
        return 'SUV';
      case VehicleType.hatchback:
        return 'Hatchback';
      case VehicleType.pickup:
        return 'Pickup Truck';
      case VehicleType.van:
        return 'Van';
      case VehicleType.motorcycle:
        return 'Motorcycle';
      case VehicleType.other:
        return 'Other';
    }
  }
}

extension VehicleStatusExtension on VehicleStatus {
  String get displayName {
    switch (this) {
      case VehicleStatus.active:
        return 'Active';
      case VehicleStatus.inactive:
        return 'Inactive';
      case VehicleStatus.maintenance:
        return 'Under Maintenance';
      case VehicleStatus.suspended:
        return 'Suspended';
    }
  }
}

extension VerificationStatusExtension on VerificationStatus {
  String get displayName {
    switch (this) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.inReview:
        return 'In Review';
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.suspended:
        return 'Suspended';
    }
  }
}

extension DayOfWeekExtension on DayOfWeek {
  String get displayName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Monday';
      case DayOfWeek.tuesday:
        return 'Tuesday';
      case DayOfWeek.wednesday:
        return 'Wednesday';
      case DayOfWeek.thursday:
        return 'Thursday';
      case DayOfWeek.friday:
        return 'Friday';
      case DayOfWeek.saturday:
        return 'Saturday';
      case DayOfWeek.sunday:
        return 'Sunday';
    }
  }

  String get shortName {
    switch (this) {
      case DayOfWeek.monday:
        return 'Mon';
      case DayOfWeek.tuesday:
        return 'Tue';
      case DayOfWeek.wednesday:
        return 'Wed';
      case DayOfWeek.thursday:
        return 'Thu';
      case DayOfWeek.friday:
        return 'Fri';
      case DayOfWeek.saturday:
        return 'Sat';
      case DayOfWeek.sunday:
        return 'Sun';
    }
  }
}

/// Driver profile creation DTO
@freezed
class CreateDriverProfileRequest with _$CreateDriverProfileRequest {
  const factory CreateDriverProfileRequest({
    required String userId,
    required String licenseNumber,
    required DateTime licenseExpiryDate,
    required String licenseClass,
    String? licenseImageUrl,
    @Default([]) List<CreateVehicleRequest> vehicles,
    @Default(false) bool acceptSharedRides,
    @Default(false) bool acceptPetFriendlyRides,
    @Default(false) bool acceptLongDistanceRides,
    @Default(false) bool acceptCashPayments,
    @Default([]) List<CreateWorkingHoursRequest> workingHours,
  }) = _CreateDriverProfileRequest;

  factory CreateDriverProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateDriverProfileRequestFromJson(json);
}

/// Vehicle creation DTO
@freezed
class CreateVehicleRequest with _$CreateVehicleRequest {
  const factory CreateVehicleRequest({
    required String make,
    required String model,
    required int year,
    required String color,
    required String licensePlate,
    required VehicleType type,
    required int seatingCapacity,
    String? imageUrl,
    String? registrationNumber,
    DateTime? registrationExpiry,
    String? insurancePolicyNumber,
    DateTime? insuranceExpiry,
  }) = _CreateVehicleRequest;

  factory CreateVehicleRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateVehicleRequestFromJson(json);
}

/// Working hours creation DTO
@freezed
class CreateWorkingHoursRequest with _$CreateWorkingHoursRequest {
  const factory CreateWorkingHoursRequest({
    required DayOfWeek dayOfWeek,
    required String startTime,
    required String endTime,
  }) = _CreateWorkingHoursRequest;

  factory CreateWorkingHoursRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateWorkingHoursRequestFromJson(json);
}
