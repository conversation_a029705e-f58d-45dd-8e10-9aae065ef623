// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'driver_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DriverProfileImpl _$$DriverProfileImplFromJson(Map<String, dynamic> json) =>
    _$DriverProfileImpl(
      id: json['id'] as String,
      userId: json['userId'] as String,
      licenseNumber: json['licenseNumber'] as String,
      licenseExpiryDate: DateTime.parse(json['licenseExpiryDate'] as String),
      licenseClass: json['licenseClass'] as String,
      licenseImageUrl: json['licenseImageUrl'] as String?,
      licenseStatus:
          $enumDecodeNullable(_$LicenseStatusEnumMap, json['licenseStatus']) ??
              LicenseStatus.pending,
      vehicles: (json['vehicles'] as List<dynamic>?)
              ?.map((e) => Vehicle.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      activeVehicleId: json['activeVehicleId'] as String?,
      acceptSharedRides: json['acceptSharedRides'] as bool? ?? false,
      acceptPetFriendlyRides: json['acceptPetFriendlyRides'] as bool? ?? false,
      acceptLongDistanceRides:
          json['acceptLongDistanceRides'] as bool? ?? false,
      acceptCashPayments: json['acceptCashPayments'] as bool? ?? false,
      isAvailable: json['isAvailable'] as bool? ?? false,
      isOnline: json['isOnline'] as bool? ?? false,
      currentLocationLatitude: json['currentLocationLatitude'] as String?,
      currentLocationLongitude: json['currentLocationLongitude'] as String?,
      workingHours: (json['workingHours'] as List<dynamic>?)
              ?.map((e) => WorkingHours.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalRides: (json['totalRides'] as num?)?.toInt() ?? 0,
      averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
      totalEarnings: (json['totalEarnings'] as num?)?.toInt() ?? 0,
      completedRides: (json['completedRides'] as num?)?.toInt() ?? 0,
      cancelledRides: (json['cancelledRides'] as num?)?.toInt() ?? 0,
      verificationStatus: $enumDecodeNullable(
              _$VerificationStatusEnumMap, json['verificationStatus']) ??
          VerificationStatus.pending,
      verificationDocuments: (json['verificationDocuments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      isProfileComplete: json['isProfileComplete'] as bool? ?? false,
      completionPercentage:
          (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$DriverProfileImplToJson(_$DriverProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate.toIso8601String(),
      'licenseClass': instance.licenseClass,
      'licenseImageUrl': instance.licenseImageUrl,
      'licenseStatus': _$LicenseStatusEnumMap[instance.licenseStatus]!,
      'vehicles': instance.vehicles,
      'activeVehicleId': instance.activeVehicleId,
      'acceptSharedRides': instance.acceptSharedRides,
      'acceptPetFriendlyRides': instance.acceptPetFriendlyRides,
      'acceptLongDistanceRides': instance.acceptLongDistanceRides,
      'acceptCashPayments': instance.acceptCashPayments,
      'isAvailable': instance.isAvailable,
      'isOnline': instance.isOnline,
      'currentLocationLatitude': instance.currentLocationLatitude,
      'currentLocationLongitude': instance.currentLocationLongitude,
      'workingHours': instance.workingHours,
      'totalRides': instance.totalRides,
      'averageRating': instance.averageRating,
      'totalEarnings': instance.totalEarnings,
      'completedRides': instance.completedRides,
      'cancelledRides': instance.cancelledRides,
      'verificationStatus':
          _$VerificationStatusEnumMap[instance.verificationStatus]!,
      'verificationDocuments': instance.verificationDocuments,
      'isProfileComplete': instance.isProfileComplete,
      'completionPercentage': instance.completionPercentage,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$LicenseStatusEnumMap = {
  LicenseStatus.pending: 'pending',
  LicenseStatus.verified: 'verified',
  LicenseStatus.rejected: 'rejected',
  LicenseStatus.expired: 'expired',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'pending',
  VerificationStatus.inReview: 'in_review',
  VerificationStatus.verified: 'verified',
  VerificationStatus.rejected: 'rejected',
  VerificationStatus.suspended: 'suspended',
};

_$VehicleImpl _$$VehicleImplFromJson(Map<String, dynamic> json) =>
    _$VehicleImpl(
      id: json['id'] as String,
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      color: json['color'] as String,
      licensePlate: json['licensePlate'] as String,
      type: $enumDecode(_$VehicleTypeEnumMap, json['type']),
      seatingCapacity: (json['seatingCapacity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      registrationNumber: json['registrationNumber'] as String?,
      registrationExpiry: json['registrationExpiry'] == null
          ? null
          : DateTime.parse(json['registrationExpiry'] as String),
      insurancePolicyNumber: json['insurancePolicyNumber'] as String?,
      insuranceExpiry: json['insuranceExpiry'] == null
          ? null
          : DateTime.parse(json['insuranceExpiry'] as String),
      status: $enumDecodeNullable(_$VehicleStatusEnumMap, json['status']) ??
          VehicleStatus.active,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$VehicleImplToJson(_$VehicleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'color': instance.color,
      'licensePlate': instance.licensePlate,
      'type': _$VehicleTypeEnumMap[instance.type]!,
      'seatingCapacity': instance.seatingCapacity,
      'imageUrl': instance.imageUrl,
      'registrationNumber': instance.registrationNumber,
      'registrationExpiry': instance.registrationExpiry?.toIso8601String(),
      'insurancePolicyNumber': instance.insurancePolicyNumber,
      'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
      'status': _$VehicleStatusEnumMap[instance.status]!,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$VehicleTypeEnumMap = {
  VehicleType.sedan: 'sedan',
  VehicleType.suv: 'suv',
  VehicleType.hatchback: 'hatchback',
  VehicleType.pickup: 'pickup',
  VehicleType.van: 'van',
  VehicleType.motorcycle: 'motorcycle',
  VehicleType.other: 'other',
};

const _$VehicleStatusEnumMap = {
  VehicleStatus.active: 'active',
  VehicleStatus.inactive: 'inactive',
  VehicleStatus.maintenance: 'maintenance',
  VehicleStatus.suspended: 'suspended',
};

_$WorkingHoursImpl _$$WorkingHoursImplFromJson(Map<String, dynamic> json) =>
    _$WorkingHoursImpl(
      id: json['id'] as String,
      dayOfWeek: $enumDecode(_$DayOfWeekEnumMap, json['dayOfWeek']),
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$WorkingHoursImplToJson(_$WorkingHoursImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'dayOfWeek': _$DayOfWeekEnumMap[instance.dayOfWeek]!,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$DayOfWeekEnumMap = {
  DayOfWeek.monday: 'monday',
  DayOfWeek.tuesday: 'tuesday',
  DayOfWeek.wednesday: 'wednesday',
  DayOfWeek.thursday: 'thursday',
  DayOfWeek.friday: 'friday',
  DayOfWeek.saturday: 'saturday',
  DayOfWeek.sunday: 'sunday',
};

_$CreateDriverProfileRequestImpl _$$CreateDriverProfileRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateDriverProfileRequestImpl(
      userId: json['userId'] as String,
      licenseNumber: json['licenseNumber'] as String,
      licenseExpiryDate: DateTime.parse(json['licenseExpiryDate'] as String),
      licenseClass: json['licenseClass'] as String,
      licenseImageUrl: json['licenseImageUrl'] as String?,
      vehicles: (json['vehicles'] as List<dynamic>?)
              ?.map((e) =>
                  CreateVehicleRequest.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      acceptSharedRides: json['acceptSharedRides'] as bool? ?? false,
      acceptPetFriendlyRides: json['acceptPetFriendlyRides'] as bool? ?? false,
      acceptLongDistanceRides:
          json['acceptLongDistanceRides'] as bool? ?? false,
      acceptCashPayments: json['acceptCashPayments'] as bool? ?? false,
      workingHours: (json['workingHours'] as List<dynamic>?)
              ?.map((e) =>
                  CreateWorkingHoursRequest.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$CreateDriverProfileRequestImplToJson(
        _$CreateDriverProfileRequestImpl instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'licenseNumber': instance.licenseNumber,
      'licenseExpiryDate': instance.licenseExpiryDate.toIso8601String(),
      'licenseClass': instance.licenseClass,
      'licenseImageUrl': instance.licenseImageUrl,
      'vehicles': instance.vehicles,
      'acceptSharedRides': instance.acceptSharedRides,
      'acceptPetFriendlyRides': instance.acceptPetFriendlyRides,
      'acceptLongDistanceRides': instance.acceptLongDistanceRides,
      'acceptCashPayments': instance.acceptCashPayments,
      'workingHours': instance.workingHours,
    };

_$CreateVehicleRequestImpl _$$CreateVehicleRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateVehicleRequestImpl(
      make: json['make'] as String,
      model: json['model'] as String,
      year: (json['year'] as num).toInt(),
      color: json['color'] as String,
      licensePlate: json['licensePlate'] as String,
      type: $enumDecode(_$VehicleTypeEnumMap, json['type']),
      seatingCapacity: (json['seatingCapacity'] as num).toInt(),
      imageUrl: json['imageUrl'] as String?,
      registrationNumber: json['registrationNumber'] as String?,
      registrationExpiry: json['registrationExpiry'] == null
          ? null
          : DateTime.parse(json['registrationExpiry'] as String),
      insurancePolicyNumber: json['insurancePolicyNumber'] as String?,
      insuranceExpiry: json['insuranceExpiry'] == null
          ? null
          : DateTime.parse(json['insuranceExpiry'] as String),
    );

Map<String, dynamic> _$$CreateVehicleRequestImplToJson(
        _$CreateVehicleRequestImpl instance) =>
    <String, dynamic>{
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'color': instance.color,
      'licensePlate': instance.licensePlate,
      'type': _$VehicleTypeEnumMap[instance.type]!,
      'seatingCapacity': instance.seatingCapacity,
      'imageUrl': instance.imageUrl,
      'registrationNumber': instance.registrationNumber,
      'registrationExpiry': instance.registrationExpiry?.toIso8601String(),
      'insurancePolicyNumber': instance.insurancePolicyNumber,
      'insuranceExpiry': instance.insuranceExpiry?.toIso8601String(),
    };

_$CreateWorkingHoursRequestImpl _$$CreateWorkingHoursRequestImplFromJson(
        Map<String, dynamic> json) =>
    _$CreateWorkingHoursRequestImpl(
      dayOfWeek: $enumDecode(_$DayOfWeekEnumMap, json['dayOfWeek']),
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
    );

Map<String, dynamic> _$$CreateWorkingHoursRequestImplToJson(
        _$CreateWorkingHoursRequestImpl instance) =>
    <String, dynamic>{
      'dayOfWeek': _$DayOfWeekEnumMap[instance.dayOfWeek]!,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
    };
