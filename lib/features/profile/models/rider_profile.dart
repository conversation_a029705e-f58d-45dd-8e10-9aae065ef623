import 'package:freezed_annotation/freezed_annotation.dart';

part 'rider_profile.freezed.dart';
part 'rider_profile.g.dart';

/// Rider-specific profile model
@freezed
class RiderProfile with _$RiderProfile {
  const factory RiderProfile({
    required String id,
    required String userId,
    
    // Emergency contacts
    @Default([]) List<EmergencyContact> emergencyContacts,
    
    // Preferences
    @Default(false) bool allowSharedRides,
    @Default(false) bool allowPetFriendlyRides,
    @Default(false) bool preferFemaleDrivers,
    @Default(false) bool accessibilityNeeds,
    
    // Accessibility details
    String? accessibilityDetails,
    
    // Favorite locations
    @Default([]) List<FavoriteLocation> favoriteLocations,
    
    // Payment preferences
    String? preferredPaymentMethod,
    
    // Ride history stats
    @Default(0) int totalRides,
    @Default(0.0) double averageRating,
    @Default(0) int totalSpent,
    
    // Profile completion
    @Default(false) bool isProfileComplete,
    @Default(0.0) double completionPercentage,
    
    // Timestamps
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _RiderProfile;

  factory RiderProfile.fromJson(Map<String, dynamic> json) =>
      _$RiderProfileFromJson(json);
}

/// Emergency contact model
@freezed
class EmergencyContact with _$EmergencyContact {
  const factory EmergencyContact({
    required String id,
    required String name,
    required String phoneNumber,
    required String relationship,
    String? email,
    @Default(true) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _EmergencyContact;

  factory EmergencyContact.fromJson(Map<String, dynamic> json) =>
      _$EmergencyContactFromJson(json);
}

/// Favorite location model
@freezed
class FavoriteLocation with _$FavoriteLocation {
  const factory FavoriteLocation({
    required String id,
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? description,
    @Default(LocationType.other) LocationType type,
    @Default(true) bool isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _FavoriteLocation;

  factory FavoriteLocation.fromJson(Map<String, dynamic> json) =>
      _$FavoriteLocationFromJson(json);
}

/// Location type enumeration
enum LocationType {
  @JsonValue('home')
  home,
  @JsonValue('work')
  work,
  @JsonValue('school')
  school,
  @JsonValue('gym')
  gym,
  @JsonValue('shopping')
  shopping,
  @JsonValue('restaurant')
  restaurant,
  @JsonValue('other')
  other,
}

/// Extension for LocationType
extension LocationTypeExtension on LocationType {
  String get displayName {
    switch (this) {
      case LocationType.home:
        return 'Home';
      case LocationType.work:
        return 'Work';
      case LocationType.school:
        return 'School';
      case LocationType.gym:
        return 'Gym';
      case LocationType.shopping:
        return 'Shopping';
      case LocationType.restaurant:
        return 'Restaurant';
      case LocationType.other:
        return 'Other';
    }
  }

  String get icon {
    switch (this) {
      case LocationType.home:
        return '🏠';
      case LocationType.work:
        return '🏢';
      case LocationType.school:
        return '🏫';
      case LocationType.gym:
        return '💪';
      case LocationType.shopping:
        return '🛒';
      case LocationType.restaurant:
        return '🍽️';
      case LocationType.other:
        return '📍';
    }
  }
}

/// Rider profile creation DTO
@freezed
class CreateRiderProfileRequest with _$CreateRiderProfileRequest {
  const factory CreateRiderProfileRequest({
    required String userId,
    @Default([]) List<CreateEmergencyContactRequest> emergencyContacts,
    @Default(false) bool allowSharedRides,
    @Default(false) bool allowPetFriendlyRides,
    @Default(false) bool preferFemaleDrivers,
    @Default(false) bool accessibilityNeeds,
    String? accessibilityDetails,
    @Default([]) List<CreateFavoriteLocationRequest> favoriteLocations,
    String? preferredPaymentMethod,
  }) = _CreateRiderProfileRequest;

  factory CreateRiderProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateRiderProfileRequestFromJson(json);
}

/// Emergency contact creation DTO
@freezed
class CreateEmergencyContactRequest with _$CreateEmergencyContactRequest {
  const factory CreateEmergencyContactRequest({
    required String name,
    required String phoneNumber,
    required String relationship,
    String? email,
  }) = _CreateEmergencyContactRequest;

  factory CreateEmergencyContactRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateEmergencyContactRequestFromJson(json);
}

/// Favorite location creation DTO
@freezed
class CreateFavoriteLocationRequest with _$CreateFavoriteLocationRequest {
  const factory CreateFavoriteLocationRequest({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    String? description,
    @Default(LocationType.other) LocationType type,
  }) = _CreateFavoriteLocationRequest;

  factory CreateFavoriteLocationRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateFavoriteLocationRequestFromJson(json);
}

/// Rider profile update DTO
@freezed
class UpdateRiderProfileRequest with _$UpdateRiderProfileRequest {
  const factory UpdateRiderProfileRequest({
    bool? allowSharedRides,
    bool? allowPetFriendlyRides,
    bool? preferFemaleDrivers,
    bool? accessibilityNeeds,
    String? accessibilityDetails,
    String? preferredPaymentMethod,
  }) = _UpdateRiderProfileRequest;

  factory UpdateRiderProfileRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateRiderProfileRequestFromJson(json);
}
