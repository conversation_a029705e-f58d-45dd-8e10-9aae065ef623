// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'driver_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DriverProfile _$DriverProfileFromJson(Map<String, dynamic> json) {
  return _DriverProfile.fromJson(json);
}

/// @nodoc
mixin _$DriverProfile {
  String get id => throw _privateConstructorUsedError;
  String get userId =>
      throw _privateConstructorUsedError; // License information
  String get licenseNumber => throw _privateConstructorUsedError;
  DateTime get licenseExpiryDate => throw _privateConstructorUsedError;
  String get licenseClass => throw _privateConstructorUsedError;
  String? get licenseImageUrl => throw _privateConstructorUsedError;
  LicenseStatus get licenseStatus =>
      throw _privateConstructorUsedError; // Vehicle information
  List<Vehicle> get vehicles => throw _privateConstructorUsedError;
  String? get activeVehicleId =>
      throw _privateConstructorUsedError; // Driver preferences
  bool get acceptSharedRides => throw _privateConstructorUsedError;
  bool get acceptPetFriendlyRides => throw _privateConstructorUsedError;
  bool get acceptLongDistanceRides => throw _privateConstructorUsedError;
  bool get acceptCashPayments =>
      throw _privateConstructorUsedError; // Availability
  bool get isAvailable => throw _privateConstructorUsedError;
  bool get isOnline => throw _privateConstructorUsedError;
  String? get currentLocationLatitude => throw _privateConstructorUsedError;
  String? get currentLocationLongitude =>
      throw _privateConstructorUsedError; // Working hours
  List<WorkingHours> get workingHours =>
      throw _privateConstructorUsedError; // Driver stats
  int get totalRides => throw _privateConstructorUsedError;
  double get averageRating => throw _privateConstructorUsedError;
  int get totalEarnings => throw _privateConstructorUsedError;
  int get completedRides => throw _privateConstructorUsedError;
  int get cancelledRides =>
      throw _privateConstructorUsedError; // Verification status
  VerificationStatus get verificationStatus =>
      throw _privateConstructorUsedError;
  List<String> get verificationDocuments =>
      throw _privateConstructorUsedError; // Profile completion
  bool get isProfileComplete => throw _privateConstructorUsedError;
  double get completionPercentage =>
      throw _privateConstructorUsedError; // Timestamps
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this DriverProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DriverProfileCopyWith<DriverProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DriverProfileCopyWith<$Res> {
  factory $DriverProfileCopyWith(
          DriverProfile value, $Res Function(DriverProfile) then) =
      _$DriverProfileCopyWithImpl<$Res, DriverProfile>;
  @useResult
  $Res call(
      {String id,
      String userId,
      String licenseNumber,
      DateTime licenseExpiryDate,
      String licenseClass,
      String? licenseImageUrl,
      LicenseStatus licenseStatus,
      List<Vehicle> vehicles,
      String? activeVehicleId,
      bool acceptSharedRides,
      bool acceptPetFriendlyRides,
      bool acceptLongDistanceRides,
      bool acceptCashPayments,
      bool isAvailable,
      bool isOnline,
      String? currentLocationLatitude,
      String? currentLocationLongitude,
      List<WorkingHours> workingHours,
      int totalRides,
      double averageRating,
      int totalEarnings,
      int completedRides,
      int cancelledRides,
      VerificationStatus verificationStatus,
      List<String> verificationDocuments,
      bool isProfileComplete,
      double completionPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$DriverProfileCopyWithImpl<$Res, $Val extends DriverProfile>
    implements $DriverProfileCopyWith<$Res> {
  _$DriverProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? licenseClass = null,
    Object? licenseImageUrl = freezed,
    Object? licenseStatus = null,
    Object? vehicles = null,
    Object? activeVehicleId = freezed,
    Object? acceptSharedRides = null,
    Object? acceptPetFriendlyRides = null,
    Object? acceptLongDistanceRides = null,
    Object? acceptCashPayments = null,
    Object? isAvailable = null,
    Object? isOnline = null,
    Object? currentLocationLatitude = freezed,
    Object? currentLocationLongitude = freezed,
    Object? workingHours = null,
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalEarnings = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? verificationStatus = null,
    Object? verificationDocuments = null,
    Object? isProfileComplete = null,
    Object? completionPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: null == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String,
      licenseExpiryDate: null == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      licenseClass: null == licenseClass
          ? _value.licenseClass
          : licenseClass // ignore: cast_nullable_to_non_nullable
              as String,
      licenseImageUrl: freezed == licenseImageUrl
          ? _value.licenseImageUrl
          : licenseImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseStatus: null == licenseStatus
          ? _value.licenseStatus
          : licenseStatus // ignore: cast_nullable_to_non_nullable
              as LicenseStatus,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      activeVehicleId: freezed == activeVehicleId
          ? _value.activeVehicleId
          : activeVehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptSharedRides: null == acceptSharedRides
          ? _value.acceptSharedRides
          : acceptSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptPetFriendlyRides: null == acceptPetFriendlyRides
          ? _value.acceptPetFriendlyRides
          : acceptPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptLongDistanceRides: null == acceptLongDistanceRides
          ? _value.acceptLongDistanceRides
          : acceptLongDistanceRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptCashPayments: null == acceptCashPayments
          ? _value.acceptCashPayments
          : acceptCashPayments // ignore: cast_nullable_to_non_nullable
              as bool,
      isAvailable: null == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnline: null == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      currentLocationLatitude: freezed == currentLocationLatitude
          ? _value.currentLocationLatitude
          : currentLocationLatitude // ignore: cast_nullable_to_non_nullable
              as String?,
      currentLocationLongitude: freezed == currentLocationLongitude
          ? _value.currentLocationLongitude
          : currentLocationLongitude // ignore: cast_nullable_to_non_nullable
              as String?,
      workingHours: null == workingHours
          ? _value.workingHours
          : workingHours // ignore: cast_nullable_to_non_nullable
              as List<WorkingHours>,
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      averageRating: null == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double,
      totalEarnings: null == totalEarnings
          ? _value.totalEarnings
          : totalEarnings // ignore: cast_nullable_to_non_nullable
              as int,
      completedRides: null == completedRides
          ? _value.completedRides
          : completedRides // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledRides: null == cancelledRides
          ? _value.cancelledRides
          : cancelledRides // ignore: cast_nullable_to_non_nullable
              as int,
      verificationStatus: null == verificationStatus
          ? _value.verificationStatus
          : verificationStatus // ignore: cast_nullable_to_non_nullable
              as VerificationStatus,
      verificationDocuments: null == verificationDocuments
          ? _value.verificationDocuments
          : verificationDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DriverProfileImplCopyWith<$Res>
    implements $DriverProfileCopyWith<$Res> {
  factory _$$DriverProfileImplCopyWith(
          _$DriverProfileImpl value, $Res Function(_$DriverProfileImpl) then) =
      __$$DriverProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String licenseNumber,
      DateTime licenseExpiryDate,
      String licenseClass,
      String? licenseImageUrl,
      LicenseStatus licenseStatus,
      List<Vehicle> vehicles,
      String? activeVehicleId,
      bool acceptSharedRides,
      bool acceptPetFriendlyRides,
      bool acceptLongDistanceRides,
      bool acceptCashPayments,
      bool isAvailable,
      bool isOnline,
      String? currentLocationLatitude,
      String? currentLocationLongitude,
      List<WorkingHours> workingHours,
      int totalRides,
      double averageRating,
      int totalEarnings,
      int completedRides,
      int cancelledRides,
      VerificationStatus verificationStatus,
      List<String> verificationDocuments,
      bool isProfileComplete,
      double completionPercentage,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$DriverProfileImplCopyWithImpl<$Res>
    extends _$DriverProfileCopyWithImpl<$Res, _$DriverProfileImpl>
    implements _$$DriverProfileImplCopyWith<$Res> {
  __$$DriverProfileImplCopyWithImpl(
      _$DriverProfileImpl _value, $Res Function(_$DriverProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? licenseClass = null,
    Object? licenseImageUrl = freezed,
    Object? licenseStatus = null,
    Object? vehicles = null,
    Object? activeVehicleId = freezed,
    Object? acceptSharedRides = null,
    Object? acceptPetFriendlyRides = null,
    Object? acceptLongDistanceRides = null,
    Object? acceptCashPayments = null,
    Object? isAvailable = null,
    Object? isOnline = null,
    Object? currentLocationLatitude = freezed,
    Object? currentLocationLongitude = freezed,
    Object? workingHours = null,
    Object? totalRides = null,
    Object? averageRating = null,
    Object? totalEarnings = null,
    Object? completedRides = null,
    Object? cancelledRides = null,
    Object? verificationStatus = null,
    Object? verificationDocuments = null,
    Object? isProfileComplete = null,
    Object? completionPercentage = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$DriverProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: null == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String,
      licenseExpiryDate: null == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      licenseClass: null == licenseClass
          ? _value.licenseClass
          : licenseClass // ignore: cast_nullable_to_non_nullable
              as String,
      licenseImageUrl: freezed == licenseImageUrl
          ? _value.licenseImageUrl
          : licenseImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      licenseStatus: null == licenseStatus
          ? _value.licenseStatus
          : licenseStatus // ignore: cast_nullable_to_non_nullable
              as LicenseStatus,
      vehicles: null == vehicles
          ? _value._vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<Vehicle>,
      activeVehicleId: freezed == activeVehicleId
          ? _value.activeVehicleId
          : activeVehicleId // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptSharedRides: null == acceptSharedRides
          ? _value.acceptSharedRides
          : acceptSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptPetFriendlyRides: null == acceptPetFriendlyRides
          ? _value.acceptPetFriendlyRides
          : acceptPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptLongDistanceRides: null == acceptLongDistanceRides
          ? _value.acceptLongDistanceRides
          : acceptLongDistanceRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptCashPayments: null == acceptCashPayments
          ? _value.acceptCashPayments
          : acceptCashPayments // ignore: cast_nullable_to_non_nullable
              as bool,
      isAvailable: null == isAvailable
          ? _value.isAvailable
          : isAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnline: null == isOnline
          ? _value.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      currentLocationLatitude: freezed == currentLocationLatitude
          ? _value.currentLocationLatitude
          : currentLocationLatitude // ignore: cast_nullable_to_non_nullable
              as String?,
      currentLocationLongitude: freezed == currentLocationLongitude
          ? _value.currentLocationLongitude
          : currentLocationLongitude // ignore: cast_nullable_to_non_nullable
              as String?,
      workingHours: null == workingHours
          ? _value._workingHours
          : workingHours // ignore: cast_nullable_to_non_nullable
              as List<WorkingHours>,
      totalRides: null == totalRides
          ? _value.totalRides
          : totalRides // ignore: cast_nullable_to_non_nullable
              as int,
      averageRating: null == averageRating
          ? _value.averageRating
          : averageRating // ignore: cast_nullable_to_non_nullable
              as double,
      totalEarnings: null == totalEarnings
          ? _value.totalEarnings
          : totalEarnings // ignore: cast_nullable_to_non_nullable
              as int,
      completedRides: null == completedRides
          ? _value.completedRides
          : completedRides // ignore: cast_nullable_to_non_nullable
              as int,
      cancelledRides: null == cancelledRides
          ? _value.cancelledRides
          : cancelledRides // ignore: cast_nullable_to_non_nullable
              as int,
      verificationStatus: null == verificationStatus
          ? _value.verificationStatus
          : verificationStatus // ignore: cast_nullable_to_non_nullable
              as VerificationStatus,
      verificationDocuments: null == verificationDocuments
          ? _value._verificationDocuments
          : verificationDocuments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      isProfileComplete: null == isProfileComplete
          ? _value.isProfileComplete
          : isProfileComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      completionPercentage: null == completionPercentage
          ? _value.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DriverProfileImpl implements _DriverProfile {
  const _$DriverProfileImpl(
      {required this.id,
      required this.userId,
      required this.licenseNumber,
      required this.licenseExpiryDate,
      required this.licenseClass,
      this.licenseImageUrl,
      this.licenseStatus = LicenseStatus.pending,
      final List<Vehicle> vehicles = const [],
      this.activeVehicleId,
      this.acceptSharedRides = false,
      this.acceptPetFriendlyRides = false,
      this.acceptLongDistanceRides = false,
      this.acceptCashPayments = false,
      this.isAvailable = false,
      this.isOnline = false,
      this.currentLocationLatitude,
      this.currentLocationLongitude,
      final List<WorkingHours> workingHours = const [],
      this.totalRides = 0,
      this.averageRating = 0.0,
      this.totalEarnings = 0,
      this.completedRides = 0,
      this.cancelledRides = 0,
      this.verificationStatus = VerificationStatus.pending,
      final List<String> verificationDocuments = const [],
      this.isProfileComplete = false,
      this.completionPercentage = 0.0,
      this.createdAt,
      this.updatedAt})
      : _vehicles = vehicles,
        _workingHours = workingHours,
        _verificationDocuments = verificationDocuments;

  factory _$DriverProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$DriverProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String userId;
// License information
  @override
  final String licenseNumber;
  @override
  final DateTime licenseExpiryDate;
  @override
  final String licenseClass;
  @override
  final String? licenseImageUrl;
  @override
  @JsonKey()
  final LicenseStatus licenseStatus;
// Vehicle information
  final List<Vehicle> _vehicles;
// Vehicle information
  @override
  @JsonKey()
  List<Vehicle> get vehicles {
    if (_vehicles is EqualUnmodifiableListView) return _vehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicles);
  }

  @override
  final String? activeVehicleId;
// Driver preferences
  @override
  @JsonKey()
  final bool acceptSharedRides;
  @override
  @JsonKey()
  final bool acceptPetFriendlyRides;
  @override
  @JsonKey()
  final bool acceptLongDistanceRides;
  @override
  @JsonKey()
  final bool acceptCashPayments;
// Availability
  @override
  @JsonKey()
  final bool isAvailable;
  @override
  @JsonKey()
  final bool isOnline;
  @override
  final String? currentLocationLatitude;
  @override
  final String? currentLocationLongitude;
// Working hours
  final List<WorkingHours> _workingHours;
// Working hours
  @override
  @JsonKey()
  List<WorkingHours> get workingHours {
    if (_workingHours is EqualUnmodifiableListView) return _workingHours;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workingHours);
  }

// Driver stats
  @override
  @JsonKey()
  final int totalRides;
  @override
  @JsonKey()
  final double averageRating;
  @override
  @JsonKey()
  final int totalEarnings;
  @override
  @JsonKey()
  final int completedRides;
  @override
  @JsonKey()
  final int cancelledRides;
// Verification status
  @override
  @JsonKey()
  final VerificationStatus verificationStatus;
  final List<String> _verificationDocuments;
  @override
  @JsonKey()
  List<String> get verificationDocuments {
    if (_verificationDocuments is EqualUnmodifiableListView)
      return _verificationDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_verificationDocuments);
  }

// Profile completion
  @override
  @JsonKey()
  final bool isProfileComplete;
  @override
  @JsonKey()
  final double completionPercentage;
// Timestamps
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'DriverProfile(id: $id, userId: $userId, licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, licenseClass: $licenseClass, licenseImageUrl: $licenseImageUrl, licenseStatus: $licenseStatus, vehicles: $vehicles, activeVehicleId: $activeVehicleId, acceptSharedRides: $acceptSharedRides, acceptPetFriendlyRides: $acceptPetFriendlyRides, acceptLongDistanceRides: $acceptLongDistanceRides, acceptCashPayments: $acceptCashPayments, isAvailable: $isAvailable, isOnline: $isOnline, currentLocationLatitude: $currentLocationLatitude, currentLocationLongitude: $currentLocationLongitude, workingHours: $workingHours, totalRides: $totalRides, averageRating: $averageRating, totalEarnings: $totalEarnings, completedRides: $completedRides, cancelledRides: $cancelledRides, verificationStatus: $verificationStatus, verificationDocuments: $verificationDocuments, isProfileComplete: $isProfileComplete, completionPercentage: $completionPercentage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DriverProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.licenseClass, licenseClass) ||
                other.licenseClass == licenseClass) &&
            (identical(other.licenseImageUrl, licenseImageUrl) ||
                other.licenseImageUrl == licenseImageUrl) &&
            (identical(other.licenseStatus, licenseStatus) ||
                other.licenseStatus == licenseStatus) &&
            const DeepCollectionEquality().equals(other._vehicles, _vehicles) &&
            (identical(other.activeVehicleId, activeVehicleId) ||
                other.activeVehicleId == activeVehicleId) &&
            (identical(other.acceptSharedRides, acceptSharedRides) ||
                other.acceptSharedRides == acceptSharedRides) &&
            (identical(other.acceptPetFriendlyRides, acceptPetFriendlyRides) ||
                other.acceptPetFriendlyRides == acceptPetFriendlyRides) &&
            (identical(other.acceptLongDistanceRides, acceptLongDistanceRides) ||
                other.acceptLongDistanceRides == acceptLongDistanceRides) &&
            (identical(other.acceptCashPayments, acceptCashPayments) ||
                other.acceptCashPayments == acceptCashPayments) &&
            (identical(other.isAvailable, isAvailable) ||
                other.isAvailable == isAvailable) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(
                    other.currentLocationLatitude, currentLocationLatitude) ||
                other.currentLocationLatitude == currentLocationLatitude) &&
            (identical(
                    other.currentLocationLongitude, currentLocationLongitude) ||
                other.currentLocationLongitude == currentLocationLongitude) &&
            const DeepCollectionEquality()
                .equals(other._workingHours, _workingHours) &&
            (identical(other.totalRides, totalRides) ||
                other.totalRides == totalRides) &&
            (identical(other.averageRating, averageRating) ||
                other.averageRating == averageRating) &&
            (identical(other.totalEarnings, totalEarnings) ||
                other.totalEarnings == totalEarnings) &&
            (identical(other.completedRides, completedRides) ||
                other.completedRides == completedRides) &&
            (identical(other.cancelledRides, cancelledRides) ||
                other.cancelledRides == cancelledRides) &&
            (identical(other.verificationStatus, verificationStatus) ||
                other.verificationStatus == verificationStatus) &&
            const DeepCollectionEquality()
                .equals(other._verificationDocuments, _verificationDocuments) &&
            (identical(other.isProfileComplete, isProfileComplete) ||
                other.isProfileComplete == isProfileComplete) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        licenseNumber,
        licenseExpiryDate,
        licenseClass,
        licenseImageUrl,
        licenseStatus,
        const DeepCollectionEquality().hash(_vehicles),
        activeVehicleId,
        acceptSharedRides,
        acceptPetFriendlyRides,
        acceptLongDistanceRides,
        acceptCashPayments,
        isAvailable,
        isOnline,
        currentLocationLatitude,
        currentLocationLongitude,
        const DeepCollectionEquality().hash(_workingHours),
        totalRides,
        averageRating,
        totalEarnings,
        completedRides,
        cancelledRides,
        verificationStatus,
        const DeepCollectionEquality().hash(_verificationDocuments),
        isProfileComplete,
        completionPercentage,
        createdAt,
        updatedAt
      ]);

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      __$$DriverProfileImplCopyWithImpl<_$DriverProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DriverProfileImplToJson(
      this,
    );
  }
}

abstract class _DriverProfile implements DriverProfile {
  const factory _DriverProfile(
      {required final String id,
      required final String userId,
      required final String licenseNumber,
      required final DateTime licenseExpiryDate,
      required final String licenseClass,
      final String? licenseImageUrl,
      final LicenseStatus licenseStatus,
      final List<Vehicle> vehicles,
      final String? activeVehicleId,
      final bool acceptSharedRides,
      final bool acceptPetFriendlyRides,
      final bool acceptLongDistanceRides,
      final bool acceptCashPayments,
      final bool isAvailable,
      final bool isOnline,
      final String? currentLocationLatitude,
      final String? currentLocationLongitude,
      final List<WorkingHours> workingHours,
      final int totalRides,
      final double averageRating,
      final int totalEarnings,
      final int completedRides,
      final int cancelledRides,
      final VerificationStatus verificationStatus,
      final List<String> verificationDocuments,
      final bool isProfileComplete,
      final double completionPercentage,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$DriverProfileImpl;

  factory _DriverProfile.fromJson(Map<String, dynamic> json) =
      _$DriverProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get userId; // License information
  @override
  String get licenseNumber;
  @override
  DateTime get licenseExpiryDate;
  @override
  String get licenseClass;
  @override
  String? get licenseImageUrl;
  @override
  LicenseStatus get licenseStatus; // Vehicle information
  @override
  List<Vehicle> get vehicles;
  @override
  String? get activeVehicleId; // Driver preferences
  @override
  bool get acceptSharedRides;
  @override
  bool get acceptPetFriendlyRides;
  @override
  bool get acceptLongDistanceRides;
  @override
  bool get acceptCashPayments; // Availability
  @override
  bool get isAvailable;
  @override
  bool get isOnline;
  @override
  String? get currentLocationLatitude;
  @override
  String? get currentLocationLongitude; // Working hours
  @override
  List<WorkingHours> get workingHours; // Driver stats
  @override
  int get totalRides;
  @override
  double get averageRating;
  @override
  int get totalEarnings;
  @override
  int get completedRides;
  @override
  int get cancelledRides; // Verification status
  @override
  VerificationStatus get verificationStatus;
  @override
  List<String> get verificationDocuments; // Profile completion
  @override
  bool get isProfileComplete;
  @override
  double get completionPercentage; // Timestamps
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of DriverProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DriverProfileImplCopyWith<_$DriverProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Vehicle _$VehicleFromJson(Map<String, dynamic> json) {
  return _Vehicle.fromJson(json);
}

/// @nodoc
mixin _$Vehicle {
  String get id => throw _privateConstructorUsedError;
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get licensePlate => throw _privateConstructorUsedError;
  VehicleType get type => throw _privateConstructorUsedError;
  int get seatingCapacity => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get registrationNumber => throw _privateConstructorUsedError;
  DateTime? get registrationExpiry => throw _privateConstructorUsedError;
  String? get insurancePolicyNumber => throw _privateConstructorUsedError;
  DateTime? get insuranceExpiry => throw _privateConstructorUsedError;
  VehicleStatus get status => throw _privateConstructorUsedError;
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Vehicle to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Vehicle
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VehicleCopyWith<Vehicle> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VehicleCopyWith<$Res> {
  factory $VehicleCopyWith(Vehicle value, $Res Function(Vehicle) then) =
      _$VehicleCopyWithImpl<$Res, Vehicle>;
  @useResult
  $Res call(
      {String id,
      String make,
      String model,
      int year,
      String color,
      String licensePlate,
      VehicleType type,
      int seatingCapacity,
      String? imageUrl,
      String? registrationNumber,
      DateTime? registrationExpiry,
      String? insurancePolicyNumber,
      DateTime? insuranceExpiry,
      VehicleStatus status,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$VehicleCopyWithImpl<$Res, $Val extends Vehicle>
    implements $VehicleCopyWith<$Res> {
  _$VehicleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Vehicle
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? type = null,
    Object? seatingCapacity = null,
    Object? imageUrl = freezed,
    Object? registrationNumber = freezed,
    Object? registrationExpiry = freezed,
    Object? insurancePolicyNumber = freezed,
    Object? insuranceExpiry = freezed,
    Object? status = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as VehicleType,
      seatingCapacity: null == seatingCapacity
          ? _value.seatingCapacity
          : seatingCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationNumber: freezed == registrationNumber
          ? _value.registrationNumber
          : registrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationExpiry: freezed == registrationExpiry
          ? _value.registrationExpiry
          : registrationExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurancePolicyNumber: freezed == insurancePolicyNumber
          ? _value.insurancePolicyNumber
          : insurancePolicyNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceExpiry: freezed == insuranceExpiry
          ? _value.insuranceExpiry
          : insuranceExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as VehicleStatus,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VehicleImplCopyWith<$Res> implements $VehicleCopyWith<$Res> {
  factory _$$VehicleImplCopyWith(
          _$VehicleImpl value, $Res Function(_$VehicleImpl) then) =
      __$$VehicleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String make,
      String model,
      int year,
      String color,
      String licensePlate,
      VehicleType type,
      int seatingCapacity,
      String? imageUrl,
      String? registrationNumber,
      DateTime? registrationExpiry,
      String? insurancePolicyNumber,
      DateTime? insuranceExpiry,
      VehicleStatus status,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$VehicleImplCopyWithImpl<$Res>
    extends _$VehicleCopyWithImpl<$Res, _$VehicleImpl>
    implements _$$VehicleImplCopyWith<$Res> {
  __$$VehicleImplCopyWithImpl(
      _$VehicleImpl _value, $Res Function(_$VehicleImpl) _then)
      : super(_value, _then);

  /// Create a copy of Vehicle
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? type = null,
    Object? seatingCapacity = null,
    Object? imageUrl = freezed,
    Object? registrationNumber = freezed,
    Object? registrationExpiry = freezed,
    Object? insurancePolicyNumber = freezed,
    Object? insuranceExpiry = freezed,
    Object? status = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$VehicleImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as VehicleType,
      seatingCapacity: null == seatingCapacity
          ? _value.seatingCapacity
          : seatingCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationNumber: freezed == registrationNumber
          ? _value.registrationNumber
          : registrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationExpiry: freezed == registrationExpiry
          ? _value.registrationExpiry
          : registrationExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurancePolicyNumber: freezed == insurancePolicyNumber
          ? _value.insurancePolicyNumber
          : insurancePolicyNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceExpiry: freezed == insuranceExpiry
          ? _value.insuranceExpiry
          : insuranceExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as VehicleStatus,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VehicleImpl implements _Vehicle {
  const _$VehicleImpl(
      {required this.id,
      required this.make,
      required this.model,
      required this.year,
      required this.color,
      required this.licensePlate,
      required this.type,
      required this.seatingCapacity,
      this.imageUrl,
      this.registrationNumber,
      this.registrationExpiry,
      this.insurancePolicyNumber,
      this.insuranceExpiry,
      this.status = VehicleStatus.active,
      this.isActive = true,
      this.createdAt,
      this.updatedAt});

  factory _$VehicleImpl.fromJson(Map<String, dynamic> json) =>
      _$$VehicleImplFromJson(json);

  @override
  final String id;
  @override
  final String make;
  @override
  final String model;
  @override
  final int year;
  @override
  final String color;
  @override
  final String licensePlate;
  @override
  final VehicleType type;
  @override
  final int seatingCapacity;
  @override
  final String? imageUrl;
  @override
  final String? registrationNumber;
  @override
  final DateTime? registrationExpiry;
  @override
  final String? insurancePolicyNumber;
  @override
  final DateTime? insuranceExpiry;
  @override
  @JsonKey()
  final VehicleStatus status;
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Vehicle(id: $id, make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate, type: $type, seatingCapacity: $seatingCapacity, imageUrl: $imageUrl, registrationNumber: $registrationNumber, registrationExpiry: $registrationExpiry, insurancePolicyNumber: $insurancePolicyNumber, insuranceExpiry: $insuranceExpiry, status: $status, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VehicleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.seatingCapacity, seatingCapacity) ||
                other.seatingCapacity == seatingCapacity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.registrationNumber, registrationNumber) ||
                other.registrationNumber == registrationNumber) &&
            (identical(other.registrationExpiry, registrationExpiry) ||
                other.registrationExpiry == registrationExpiry) &&
            (identical(other.insurancePolicyNumber, insurancePolicyNumber) ||
                other.insurancePolicyNumber == insurancePolicyNumber) &&
            (identical(other.insuranceExpiry, insuranceExpiry) ||
                other.insuranceExpiry == insuranceExpiry) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      make,
      model,
      year,
      color,
      licensePlate,
      type,
      seatingCapacity,
      imageUrl,
      registrationNumber,
      registrationExpiry,
      insurancePolicyNumber,
      insuranceExpiry,
      status,
      isActive,
      createdAt,
      updatedAt);

  /// Create a copy of Vehicle
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VehicleImplCopyWith<_$VehicleImpl> get copyWith =>
      __$$VehicleImplCopyWithImpl<_$VehicleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VehicleImplToJson(
      this,
    );
  }
}

abstract class _Vehicle implements Vehicle {
  const factory _Vehicle(
      {required final String id,
      required final String make,
      required final String model,
      required final int year,
      required final String color,
      required final String licensePlate,
      required final VehicleType type,
      required final int seatingCapacity,
      final String? imageUrl,
      final String? registrationNumber,
      final DateTime? registrationExpiry,
      final String? insurancePolicyNumber,
      final DateTime? insuranceExpiry,
      final VehicleStatus status,
      final bool isActive,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$VehicleImpl;

  factory _Vehicle.fromJson(Map<String, dynamic> json) = _$VehicleImpl.fromJson;

  @override
  String get id;
  @override
  String get make;
  @override
  String get model;
  @override
  int get year;
  @override
  String get color;
  @override
  String get licensePlate;
  @override
  VehicleType get type;
  @override
  int get seatingCapacity;
  @override
  String? get imageUrl;
  @override
  String? get registrationNumber;
  @override
  DateTime? get registrationExpiry;
  @override
  String? get insurancePolicyNumber;
  @override
  DateTime? get insuranceExpiry;
  @override
  VehicleStatus get status;
  @override
  bool get isActive;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of Vehicle
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VehicleImplCopyWith<_$VehicleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorkingHours _$WorkingHoursFromJson(Map<String, dynamic> json) {
  return _WorkingHours.fromJson(json);
}

/// @nodoc
mixin _$WorkingHours {
  String get id => throw _privateConstructorUsedError;
  DayOfWeek get dayOfWeek => throw _privateConstructorUsedError;
  String get startTime => throw _privateConstructorUsedError; // Format: "HH:mm"
  String get endTime => throw _privateConstructorUsedError; // Format: "HH:mm"
  bool get isActive => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this WorkingHours to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkingHoursCopyWith<WorkingHours> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkingHoursCopyWith<$Res> {
  factory $WorkingHoursCopyWith(
          WorkingHours value, $Res Function(WorkingHours) then) =
      _$WorkingHoursCopyWithImpl<$Res, WorkingHours>;
  @useResult
  $Res call(
      {String id,
      DayOfWeek dayOfWeek,
      String startTime,
      String endTime,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$WorkingHoursCopyWithImpl<$Res, $Val extends WorkingHours>
    implements $WorkingHoursCopyWith<$Res> {
  _$WorkingHoursCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? dayOfWeek = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      dayOfWeek: null == dayOfWeek
          ? _value.dayOfWeek
          : dayOfWeek // ignore: cast_nullable_to_non_nullable
              as DayOfWeek,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkingHoursImplCopyWith<$Res>
    implements $WorkingHoursCopyWith<$Res> {
  factory _$$WorkingHoursImplCopyWith(
          _$WorkingHoursImpl value, $Res Function(_$WorkingHoursImpl) then) =
      __$$WorkingHoursImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      DayOfWeek dayOfWeek,
      String startTime,
      String endTime,
      bool isActive,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$WorkingHoursImplCopyWithImpl<$Res>
    extends _$WorkingHoursCopyWithImpl<$Res, _$WorkingHoursImpl>
    implements _$$WorkingHoursImplCopyWith<$Res> {
  __$$WorkingHoursImplCopyWithImpl(
      _$WorkingHoursImpl _value, $Res Function(_$WorkingHoursImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? dayOfWeek = null,
    Object? startTime = null,
    Object? endTime = null,
    Object? isActive = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$WorkingHoursImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      dayOfWeek: null == dayOfWeek
          ? _value.dayOfWeek
          : dayOfWeek // ignore: cast_nullable_to_non_nullable
              as DayOfWeek,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkingHoursImpl implements _WorkingHours {
  const _$WorkingHoursImpl(
      {required this.id,
      required this.dayOfWeek,
      required this.startTime,
      required this.endTime,
      this.isActive = true,
      this.createdAt,
      this.updatedAt});

  factory _$WorkingHoursImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkingHoursImplFromJson(json);

  @override
  final String id;
  @override
  final DayOfWeek dayOfWeek;
  @override
  final String startTime;
// Format: "HH:mm"
  @override
  final String endTime;
// Format: "HH:mm"
  @override
  @JsonKey()
  final bool isActive;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'WorkingHours(id: $id, dayOfWeek: $dayOfWeek, startTime: $startTime, endTime: $endTime, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkingHoursImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.dayOfWeek, dayOfWeek) ||
                other.dayOfWeek == dayOfWeek) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, dayOfWeek, startTime,
      endTime, isActive, createdAt, updatedAt);

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkingHoursImplCopyWith<_$WorkingHoursImpl> get copyWith =>
      __$$WorkingHoursImplCopyWithImpl<_$WorkingHoursImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkingHoursImplToJson(
      this,
    );
  }
}

abstract class _WorkingHours implements WorkingHours {
  const factory _WorkingHours(
      {required final String id,
      required final DayOfWeek dayOfWeek,
      required final String startTime,
      required final String endTime,
      final bool isActive,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$WorkingHoursImpl;

  factory _WorkingHours.fromJson(Map<String, dynamic> json) =
      _$WorkingHoursImpl.fromJson;

  @override
  String get id;
  @override
  DayOfWeek get dayOfWeek;
  @override
  String get startTime; // Format: "HH:mm"
  @override
  String get endTime; // Format: "HH:mm"
  @override
  bool get isActive;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of WorkingHours
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkingHoursImplCopyWith<_$WorkingHoursImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreateDriverProfileRequest _$CreateDriverProfileRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateDriverProfileRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateDriverProfileRequest {
  String get userId => throw _privateConstructorUsedError;
  String get licenseNumber => throw _privateConstructorUsedError;
  DateTime get licenseExpiryDate => throw _privateConstructorUsedError;
  String get licenseClass => throw _privateConstructorUsedError;
  String? get licenseImageUrl => throw _privateConstructorUsedError;
  List<CreateVehicleRequest> get vehicles => throw _privateConstructorUsedError;
  bool get acceptSharedRides => throw _privateConstructorUsedError;
  bool get acceptPetFriendlyRides => throw _privateConstructorUsedError;
  bool get acceptLongDistanceRides => throw _privateConstructorUsedError;
  bool get acceptCashPayments => throw _privateConstructorUsedError;
  List<CreateWorkingHoursRequest> get workingHours =>
      throw _privateConstructorUsedError;

  /// Serializes this CreateDriverProfileRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateDriverProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateDriverProfileRequestCopyWith<CreateDriverProfileRequest>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateDriverProfileRequestCopyWith<$Res> {
  factory $CreateDriverProfileRequestCopyWith(CreateDriverProfileRequest value,
          $Res Function(CreateDriverProfileRequest) then) =
      _$CreateDriverProfileRequestCopyWithImpl<$Res,
          CreateDriverProfileRequest>;
  @useResult
  $Res call(
      {String userId,
      String licenseNumber,
      DateTime licenseExpiryDate,
      String licenseClass,
      String? licenseImageUrl,
      List<CreateVehicleRequest> vehicles,
      bool acceptSharedRides,
      bool acceptPetFriendlyRides,
      bool acceptLongDistanceRides,
      bool acceptCashPayments,
      List<CreateWorkingHoursRequest> workingHours});
}

/// @nodoc
class _$CreateDriverProfileRequestCopyWithImpl<$Res,
        $Val extends CreateDriverProfileRequest>
    implements $CreateDriverProfileRequestCopyWith<$Res> {
  _$CreateDriverProfileRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateDriverProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? licenseClass = null,
    Object? licenseImageUrl = freezed,
    Object? vehicles = null,
    Object? acceptSharedRides = null,
    Object? acceptPetFriendlyRides = null,
    Object? acceptLongDistanceRides = null,
    Object? acceptCashPayments = null,
    Object? workingHours = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: null == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String,
      licenseExpiryDate: null == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      licenseClass: null == licenseClass
          ? _value.licenseClass
          : licenseClass // ignore: cast_nullable_to_non_nullable
              as String,
      licenseImageUrl: freezed == licenseImageUrl
          ? _value.licenseImageUrl
          : licenseImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicles: null == vehicles
          ? _value.vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<CreateVehicleRequest>,
      acceptSharedRides: null == acceptSharedRides
          ? _value.acceptSharedRides
          : acceptSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptPetFriendlyRides: null == acceptPetFriendlyRides
          ? _value.acceptPetFriendlyRides
          : acceptPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptLongDistanceRides: null == acceptLongDistanceRides
          ? _value.acceptLongDistanceRides
          : acceptLongDistanceRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptCashPayments: null == acceptCashPayments
          ? _value.acceptCashPayments
          : acceptCashPayments // ignore: cast_nullable_to_non_nullable
              as bool,
      workingHours: null == workingHours
          ? _value.workingHours
          : workingHours // ignore: cast_nullable_to_non_nullable
              as List<CreateWorkingHoursRequest>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateDriverProfileRequestImplCopyWith<$Res>
    implements $CreateDriverProfileRequestCopyWith<$Res> {
  factory _$$CreateDriverProfileRequestImplCopyWith(
          _$CreateDriverProfileRequestImpl value,
          $Res Function(_$CreateDriverProfileRequestImpl) then) =
      __$$CreateDriverProfileRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String userId,
      String licenseNumber,
      DateTime licenseExpiryDate,
      String licenseClass,
      String? licenseImageUrl,
      List<CreateVehicleRequest> vehicles,
      bool acceptSharedRides,
      bool acceptPetFriendlyRides,
      bool acceptLongDistanceRides,
      bool acceptCashPayments,
      List<CreateWorkingHoursRequest> workingHours});
}

/// @nodoc
class __$$CreateDriverProfileRequestImplCopyWithImpl<$Res>
    extends _$CreateDriverProfileRequestCopyWithImpl<$Res,
        _$CreateDriverProfileRequestImpl>
    implements _$$CreateDriverProfileRequestImplCopyWith<$Res> {
  __$$CreateDriverProfileRequestImplCopyWithImpl(
      _$CreateDriverProfileRequestImpl _value,
      $Res Function(_$CreateDriverProfileRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateDriverProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? licenseNumber = null,
    Object? licenseExpiryDate = null,
    Object? licenseClass = null,
    Object? licenseImageUrl = freezed,
    Object? vehicles = null,
    Object? acceptSharedRides = null,
    Object? acceptPetFriendlyRides = null,
    Object? acceptLongDistanceRides = null,
    Object? acceptCashPayments = null,
    Object? workingHours = null,
  }) {
    return _then(_$CreateDriverProfileRequestImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      licenseNumber: null == licenseNumber
          ? _value.licenseNumber
          : licenseNumber // ignore: cast_nullable_to_non_nullable
              as String,
      licenseExpiryDate: null == licenseExpiryDate
          ? _value.licenseExpiryDate
          : licenseExpiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      licenseClass: null == licenseClass
          ? _value.licenseClass
          : licenseClass // ignore: cast_nullable_to_non_nullable
              as String,
      licenseImageUrl: freezed == licenseImageUrl
          ? _value.licenseImageUrl
          : licenseImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      vehicles: null == vehicles
          ? _value._vehicles
          : vehicles // ignore: cast_nullable_to_non_nullable
              as List<CreateVehicleRequest>,
      acceptSharedRides: null == acceptSharedRides
          ? _value.acceptSharedRides
          : acceptSharedRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptPetFriendlyRides: null == acceptPetFriendlyRides
          ? _value.acceptPetFriendlyRides
          : acceptPetFriendlyRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptLongDistanceRides: null == acceptLongDistanceRides
          ? _value.acceptLongDistanceRides
          : acceptLongDistanceRides // ignore: cast_nullable_to_non_nullable
              as bool,
      acceptCashPayments: null == acceptCashPayments
          ? _value.acceptCashPayments
          : acceptCashPayments // ignore: cast_nullable_to_non_nullable
              as bool,
      workingHours: null == workingHours
          ? _value._workingHours
          : workingHours // ignore: cast_nullable_to_non_nullable
              as List<CreateWorkingHoursRequest>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateDriverProfileRequestImpl implements _CreateDriverProfileRequest {
  const _$CreateDriverProfileRequestImpl(
      {required this.userId,
      required this.licenseNumber,
      required this.licenseExpiryDate,
      required this.licenseClass,
      this.licenseImageUrl,
      final List<CreateVehicleRequest> vehicles = const [],
      this.acceptSharedRides = false,
      this.acceptPetFriendlyRides = false,
      this.acceptLongDistanceRides = false,
      this.acceptCashPayments = false,
      final List<CreateWorkingHoursRequest> workingHours = const []})
      : _vehicles = vehicles,
        _workingHours = workingHours;

  factory _$CreateDriverProfileRequestImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$CreateDriverProfileRequestImplFromJson(json);

  @override
  final String userId;
  @override
  final String licenseNumber;
  @override
  final DateTime licenseExpiryDate;
  @override
  final String licenseClass;
  @override
  final String? licenseImageUrl;
  final List<CreateVehicleRequest> _vehicles;
  @override
  @JsonKey()
  List<CreateVehicleRequest> get vehicles {
    if (_vehicles is EqualUnmodifiableListView) return _vehicles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_vehicles);
  }

  @override
  @JsonKey()
  final bool acceptSharedRides;
  @override
  @JsonKey()
  final bool acceptPetFriendlyRides;
  @override
  @JsonKey()
  final bool acceptLongDistanceRides;
  @override
  @JsonKey()
  final bool acceptCashPayments;
  final List<CreateWorkingHoursRequest> _workingHours;
  @override
  @JsonKey()
  List<CreateWorkingHoursRequest> get workingHours {
    if (_workingHours is EqualUnmodifiableListView) return _workingHours;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workingHours);
  }

  @override
  String toString() {
    return 'CreateDriverProfileRequest(userId: $userId, licenseNumber: $licenseNumber, licenseExpiryDate: $licenseExpiryDate, licenseClass: $licenseClass, licenseImageUrl: $licenseImageUrl, vehicles: $vehicles, acceptSharedRides: $acceptSharedRides, acceptPetFriendlyRides: $acceptPetFriendlyRides, acceptLongDistanceRides: $acceptLongDistanceRides, acceptCashPayments: $acceptCashPayments, workingHours: $workingHours)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateDriverProfileRequestImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.licenseNumber, licenseNumber) ||
                other.licenseNumber == licenseNumber) &&
            (identical(other.licenseExpiryDate, licenseExpiryDate) ||
                other.licenseExpiryDate == licenseExpiryDate) &&
            (identical(other.licenseClass, licenseClass) ||
                other.licenseClass == licenseClass) &&
            (identical(other.licenseImageUrl, licenseImageUrl) ||
                other.licenseImageUrl == licenseImageUrl) &&
            const DeepCollectionEquality().equals(other._vehicles, _vehicles) &&
            (identical(other.acceptSharedRides, acceptSharedRides) ||
                other.acceptSharedRides == acceptSharedRides) &&
            (identical(other.acceptPetFriendlyRides, acceptPetFriendlyRides) ||
                other.acceptPetFriendlyRides == acceptPetFriendlyRides) &&
            (identical(
                    other.acceptLongDistanceRides, acceptLongDistanceRides) ||
                other.acceptLongDistanceRides == acceptLongDistanceRides) &&
            (identical(other.acceptCashPayments, acceptCashPayments) ||
                other.acceptCashPayments == acceptCashPayments) &&
            const DeepCollectionEquality()
                .equals(other._workingHours, _workingHours));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userId,
      licenseNumber,
      licenseExpiryDate,
      licenseClass,
      licenseImageUrl,
      const DeepCollectionEquality().hash(_vehicles),
      acceptSharedRides,
      acceptPetFriendlyRides,
      acceptLongDistanceRides,
      acceptCashPayments,
      const DeepCollectionEquality().hash(_workingHours));

  /// Create a copy of CreateDriverProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateDriverProfileRequestImplCopyWith<_$CreateDriverProfileRequestImpl>
      get copyWith => __$$CreateDriverProfileRequestImplCopyWithImpl<
          _$CreateDriverProfileRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateDriverProfileRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateDriverProfileRequest
    implements CreateDriverProfileRequest {
  const factory _CreateDriverProfileRequest(
          {required final String userId,
          required final String licenseNumber,
          required final DateTime licenseExpiryDate,
          required final String licenseClass,
          final String? licenseImageUrl,
          final List<CreateVehicleRequest> vehicles,
          final bool acceptSharedRides,
          final bool acceptPetFriendlyRides,
          final bool acceptLongDistanceRides,
          final bool acceptCashPayments,
          final List<CreateWorkingHoursRequest> workingHours}) =
      _$CreateDriverProfileRequestImpl;

  factory _CreateDriverProfileRequest.fromJson(Map<String, dynamic> json) =
      _$CreateDriverProfileRequestImpl.fromJson;

  @override
  String get userId;
  @override
  String get licenseNumber;
  @override
  DateTime get licenseExpiryDate;
  @override
  String get licenseClass;
  @override
  String? get licenseImageUrl;
  @override
  List<CreateVehicleRequest> get vehicles;
  @override
  bool get acceptSharedRides;
  @override
  bool get acceptPetFriendlyRides;
  @override
  bool get acceptLongDistanceRides;
  @override
  bool get acceptCashPayments;
  @override
  List<CreateWorkingHoursRequest> get workingHours;

  /// Create a copy of CreateDriverProfileRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateDriverProfileRequestImplCopyWith<_$CreateDriverProfileRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreateVehicleRequest _$CreateVehicleRequestFromJson(Map<String, dynamic> json) {
  return _CreateVehicleRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateVehicleRequest {
  String get make => throw _privateConstructorUsedError;
  String get model => throw _privateConstructorUsedError;
  int get year => throw _privateConstructorUsedError;
  String get color => throw _privateConstructorUsedError;
  String get licensePlate => throw _privateConstructorUsedError;
  VehicleType get type => throw _privateConstructorUsedError;
  int get seatingCapacity => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get registrationNumber => throw _privateConstructorUsedError;
  DateTime? get registrationExpiry => throw _privateConstructorUsedError;
  String? get insurancePolicyNumber => throw _privateConstructorUsedError;
  DateTime? get insuranceExpiry => throw _privateConstructorUsedError;

  /// Serializes this CreateVehicleRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateVehicleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateVehicleRequestCopyWith<CreateVehicleRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateVehicleRequestCopyWith<$Res> {
  factory $CreateVehicleRequestCopyWith(CreateVehicleRequest value,
          $Res Function(CreateVehicleRequest) then) =
      _$CreateVehicleRequestCopyWithImpl<$Res, CreateVehicleRequest>;
  @useResult
  $Res call(
      {String make,
      String model,
      int year,
      String color,
      String licensePlate,
      VehicleType type,
      int seatingCapacity,
      String? imageUrl,
      String? registrationNumber,
      DateTime? registrationExpiry,
      String? insurancePolicyNumber,
      DateTime? insuranceExpiry});
}

/// @nodoc
class _$CreateVehicleRequestCopyWithImpl<$Res,
        $Val extends CreateVehicleRequest>
    implements $CreateVehicleRequestCopyWith<$Res> {
  _$CreateVehicleRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateVehicleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? type = null,
    Object? seatingCapacity = null,
    Object? imageUrl = freezed,
    Object? registrationNumber = freezed,
    Object? registrationExpiry = freezed,
    Object? insurancePolicyNumber = freezed,
    Object? insuranceExpiry = freezed,
  }) {
    return _then(_value.copyWith(
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as VehicleType,
      seatingCapacity: null == seatingCapacity
          ? _value.seatingCapacity
          : seatingCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationNumber: freezed == registrationNumber
          ? _value.registrationNumber
          : registrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationExpiry: freezed == registrationExpiry
          ? _value.registrationExpiry
          : registrationExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurancePolicyNumber: freezed == insurancePolicyNumber
          ? _value.insurancePolicyNumber
          : insurancePolicyNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceExpiry: freezed == insuranceExpiry
          ? _value.insuranceExpiry
          : insuranceExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateVehicleRequestImplCopyWith<$Res>
    implements $CreateVehicleRequestCopyWith<$Res> {
  factory _$$CreateVehicleRequestImplCopyWith(_$CreateVehicleRequestImpl value,
          $Res Function(_$CreateVehicleRequestImpl) then) =
      __$$CreateVehicleRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String make,
      String model,
      int year,
      String color,
      String licensePlate,
      VehicleType type,
      int seatingCapacity,
      String? imageUrl,
      String? registrationNumber,
      DateTime? registrationExpiry,
      String? insurancePolicyNumber,
      DateTime? insuranceExpiry});
}

/// @nodoc
class __$$CreateVehicleRequestImplCopyWithImpl<$Res>
    extends _$CreateVehicleRequestCopyWithImpl<$Res, _$CreateVehicleRequestImpl>
    implements _$$CreateVehicleRequestImplCopyWith<$Res> {
  __$$CreateVehicleRequestImplCopyWithImpl(_$CreateVehicleRequestImpl _value,
      $Res Function(_$CreateVehicleRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateVehicleRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? make = null,
    Object? model = null,
    Object? year = null,
    Object? color = null,
    Object? licensePlate = null,
    Object? type = null,
    Object? seatingCapacity = null,
    Object? imageUrl = freezed,
    Object? registrationNumber = freezed,
    Object? registrationExpiry = freezed,
    Object? insurancePolicyNumber = freezed,
    Object? insuranceExpiry = freezed,
  }) {
    return _then(_$CreateVehicleRequestImpl(
      make: null == make
          ? _value.make
          : make // ignore: cast_nullable_to_non_nullable
              as String,
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as String,
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      licensePlate: null == licensePlate
          ? _value.licensePlate
          : licensePlate // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as VehicleType,
      seatingCapacity: null == seatingCapacity
          ? _value.seatingCapacity
          : seatingCapacity // ignore: cast_nullable_to_non_nullable
              as int,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationNumber: freezed == registrationNumber
          ? _value.registrationNumber
          : registrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationExpiry: freezed == registrationExpiry
          ? _value.registrationExpiry
          : registrationExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      insurancePolicyNumber: freezed == insurancePolicyNumber
          ? _value.insurancePolicyNumber
          : insurancePolicyNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      insuranceExpiry: freezed == insuranceExpiry
          ? _value.insuranceExpiry
          : insuranceExpiry // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateVehicleRequestImpl implements _CreateVehicleRequest {
  const _$CreateVehicleRequestImpl(
      {required this.make,
      required this.model,
      required this.year,
      required this.color,
      required this.licensePlate,
      required this.type,
      required this.seatingCapacity,
      this.imageUrl,
      this.registrationNumber,
      this.registrationExpiry,
      this.insurancePolicyNumber,
      this.insuranceExpiry});

  factory _$CreateVehicleRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateVehicleRequestImplFromJson(json);

  @override
  final String make;
  @override
  final String model;
  @override
  final int year;
  @override
  final String color;
  @override
  final String licensePlate;
  @override
  final VehicleType type;
  @override
  final int seatingCapacity;
  @override
  final String? imageUrl;
  @override
  final String? registrationNumber;
  @override
  final DateTime? registrationExpiry;
  @override
  final String? insurancePolicyNumber;
  @override
  final DateTime? insuranceExpiry;

  @override
  String toString() {
    return 'CreateVehicleRequest(make: $make, model: $model, year: $year, color: $color, licensePlate: $licensePlate, type: $type, seatingCapacity: $seatingCapacity, imageUrl: $imageUrl, registrationNumber: $registrationNumber, registrationExpiry: $registrationExpiry, insurancePolicyNumber: $insurancePolicyNumber, insuranceExpiry: $insuranceExpiry)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateVehicleRequestImpl &&
            (identical(other.make, make) || other.make == make) &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.licensePlate, licensePlate) ||
                other.licensePlate == licensePlate) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.seatingCapacity, seatingCapacity) ||
                other.seatingCapacity == seatingCapacity) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.registrationNumber, registrationNumber) ||
                other.registrationNumber == registrationNumber) &&
            (identical(other.registrationExpiry, registrationExpiry) ||
                other.registrationExpiry == registrationExpiry) &&
            (identical(other.insurancePolicyNumber, insurancePolicyNumber) ||
                other.insurancePolicyNumber == insurancePolicyNumber) &&
            (identical(other.insuranceExpiry, insuranceExpiry) ||
                other.insuranceExpiry == insuranceExpiry));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      make,
      model,
      year,
      color,
      licensePlate,
      type,
      seatingCapacity,
      imageUrl,
      registrationNumber,
      registrationExpiry,
      insurancePolicyNumber,
      insuranceExpiry);

  /// Create a copy of CreateVehicleRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateVehicleRequestImplCopyWith<_$CreateVehicleRequestImpl>
      get copyWith =>
          __$$CreateVehicleRequestImplCopyWithImpl<_$CreateVehicleRequestImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateVehicleRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateVehicleRequest implements CreateVehicleRequest {
  const factory _CreateVehicleRequest(
      {required final String make,
      required final String model,
      required final int year,
      required final String color,
      required final String licensePlate,
      required final VehicleType type,
      required final int seatingCapacity,
      final String? imageUrl,
      final String? registrationNumber,
      final DateTime? registrationExpiry,
      final String? insurancePolicyNumber,
      final DateTime? insuranceExpiry}) = _$CreateVehicleRequestImpl;

  factory _CreateVehicleRequest.fromJson(Map<String, dynamic> json) =
      _$CreateVehicleRequestImpl.fromJson;

  @override
  String get make;
  @override
  String get model;
  @override
  int get year;
  @override
  String get color;
  @override
  String get licensePlate;
  @override
  VehicleType get type;
  @override
  int get seatingCapacity;
  @override
  String? get imageUrl;
  @override
  String? get registrationNumber;
  @override
  DateTime? get registrationExpiry;
  @override
  String? get insurancePolicyNumber;
  @override
  DateTime? get insuranceExpiry;

  /// Create a copy of CreateVehicleRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateVehicleRequestImplCopyWith<_$CreateVehicleRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

CreateWorkingHoursRequest _$CreateWorkingHoursRequestFromJson(
    Map<String, dynamic> json) {
  return _CreateWorkingHoursRequest.fromJson(json);
}

/// @nodoc
mixin _$CreateWorkingHoursRequest {
  DayOfWeek get dayOfWeek => throw _privateConstructorUsedError;
  String get startTime => throw _privateConstructorUsedError;
  String get endTime => throw _privateConstructorUsedError;

  /// Serializes this CreateWorkingHoursRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreateWorkingHoursRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreateWorkingHoursRequestCopyWith<CreateWorkingHoursRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreateWorkingHoursRequestCopyWith<$Res> {
  factory $CreateWorkingHoursRequestCopyWith(CreateWorkingHoursRequest value,
          $Res Function(CreateWorkingHoursRequest) then) =
      _$CreateWorkingHoursRequestCopyWithImpl<$Res, CreateWorkingHoursRequest>;
  @useResult
  $Res call({DayOfWeek dayOfWeek, String startTime, String endTime});
}

/// @nodoc
class _$CreateWorkingHoursRequestCopyWithImpl<$Res,
        $Val extends CreateWorkingHoursRequest>
    implements $CreateWorkingHoursRequestCopyWith<$Res> {
  _$CreateWorkingHoursRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreateWorkingHoursRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dayOfWeek = null,
    Object? startTime = null,
    Object? endTime = null,
  }) {
    return _then(_value.copyWith(
      dayOfWeek: null == dayOfWeek
          ? _value.dayOfWeek
          : dayOfWeek // ignore: cast_nullable_to_non_nullable
              as DayOfWeek,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CreateWorkingHoursRequestImplCopyWith<$Res>
    implements $CreateWorkingHoursRequestCopyWith<$Res> {
  factory _$$CreateWorkingHoursRequestImplCopyWith(
          _$CreateWorkingHoursRequestImpl value,
          $Res Function(_$CreateWorkingHoursRequestImpl) then) =
      __$$CreateWorkingHoursRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DayOfWeek dayOfWeek, String startTime, String endTime});
}

/// @nodoc
class __$$CreateWorkingHoursRequestImplCopyWithImpl<$Res>
    extends _$CreateWorkingHoursRequestCopyWithImpl<$Res,
        _$CreateWorkingHoursRequestImpl>
    implements _$$CreateWorkingHoursRequestImplCopyWith<$Res> {
  __$$CreateWorkingHoursRequestImplCopyWithImpl(
      _$CreateWorkingHoursRequestImpl _value,
      $Res Function(_$CreateWorkingHoursRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreateWorkingHoursRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dayOfWeek = null,
    Object? startTime = null,
    Object? endTime = null,
  }) {
    return _then(_$CreateWorkingHoursRequestImpl(
      dayOfWeek: null == dayOfWeek
          ? _value.dayOfWeek
          : dayOfWeek // ignore: cast_nullable_to_non_nullable
              as DayOfWeek,
      startTime: null == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as String,
      endTime: null == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CreateWorkingHoursRequestImpl implements _CreateWorkingHoursRequest {
  const _$CreateWorkingHoursRequestImpl(
      {required this.dayOfWeek,
      required this.startTime,
      required this.endTime});

  factory _$CreateWorkingHoursRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$CreateWorkingHoursRequestImplFromJson(json);

  @override
  final DayOfWeek dayOfWeek;
  @override
  final String startTime;
  @override
  final String endTime;

  @override
  String toString() {
    return 'CreateWorkingHoursRequest(dayOfWeek: $dayOfWeek, startTime: $startTime, endTime: $endTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateWorkingHoursRequestImpl &&
            (identical(other.dayOfWeek, dayOfWeek) ||
                other.dayOfWeek == dayOfWeek) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, dayOfWeek, startTime, endTime);

  /// Create a copy of CreateWorkingHoursRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateWorkingHoursRequestImplCopyWith<_$CreateWorkingHoursRequestImpl>
      get copyWith => __$$CreateWorkingHoursRequestImplCopyWithImpl<
          _$CreateWorkingHoursRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CreateWorkingHoursRequestImplToJson(
      this,
    );
  }
}

abstract class _CreateWorkingHoursRequest implements CreateWorkingHoursRequest {
  const factory _CreateWorkingHoursRequest(
      {required final DayOfWeek dayOfWeek,
      required final String startTime,
      required final String endTime}) = _$CreateWorkingHoursRequestImpl;

  factory _CreateWorkingHoursRequest.fromJson(Map<String, dynamic> json) =
      _$CreateWorkingHoursRequestImpl.fromJson;

  @override
  DayOfWeek get dayOfWeek;
  @override
  String get startTime;
  @override
  String get endTime;

  /// Create a copy of CreateWorkingHoursRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateWorkingHoursRequestImplCopyWith<_$CreateWorkingHoursRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}
