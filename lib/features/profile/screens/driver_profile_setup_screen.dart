import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/theme/index.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../models/driver_profile.dart';

/// Driver profile setup screen for license and vehicle information
class DriverProfileSetupScreen extends ConsumerStatefulWidget {
  const DriverProfileSetupScreen({super.key});

  @override
  ConsumerState<DriverProfileSetupScreen> createState() => _DriverProfileSetupScreenState();
}

class _DriverProfileSetupScreenState extends ConsumerState<DriverProfileSetupScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 4;

  // License information
  final TextEditingController _licenseNumberController = TextEditingController();
  final TextEditingController _licenseClassController = TextEditingController();
  DateTime? _licenseExpiryDate;
  String? _licenseImagePath;

  // Vehicle information
  final List<VehicleForm> _vehicles = [];

  // Driver preferences
  bool _acceptSharedRides = false;
  bool _acceptPetFriendlyRides = false;
  bool _acceptLongDistanceRides = false;
  bool _acceptCashPayments = true;

  // Working hours
  final Map<DayOfWeek, WorkingHoursForm> _workingHours = {};

  @override
  void initState() {
    super.initState();
    // Add one vehicle by default
    _addVehicle();
    // Initialize working hours
    _initializeWorkingHours();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _licenseNumberController.dispose();
    _licenseClassController.dispose();
    for (final vehicle in _vehicles) {
      vehicle.dispose();
    }
    for (final hours in _workingHours.values) {
      hours.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Driver Profile Setup'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: false, // TODO: Connect to state management
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            
            const SizedBox(height: AppSpacing.lg),
            
            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  _buildLicenseStep(),
                  _buildVehicleStep(),
                  _buildPreferencesStep(),
                  _buildWorkingHoursStep(),
                ],
              ),
            ),
            
            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.containerPadding),
      child: Column(
        children: [
          Row(
            children: List.generate(_totalSteps, (index) {
              final bool isActive = index <= _currentStep;
              
              return Expanded(
                child: Container(
                  height: 4,
                  margin: EdgeInsets.only(
                    right: index < _totalSteps - 1 ? AppSpacing.sm : 0,
                  ),
                  decoration: BoxDecoration(
                    color: isActive ? AppColors.primary : AppColors.lightGray,
                    borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                  ),
                ),
              );
            }),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          Text(
            'Step ${_currentStep + 1} of $_totalSteps',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Driver\'s License',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Please provide your driver\'s license information for verification.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          CustomTextField(
            controller: _licenseNumberController,
            label: 'License Number',
            hintText: 'Enter your license number',
            prefixIcon: Icons.credit_card,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'License number is required';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          CustomTextField(
            controller: _licenseClassController,
            label: 'License Class',
            hintText: 'e.g., Class B, Class C',
            prefixIcon: Icons.category,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'License class is required';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // License expiry date
          InkWell(
            onTap: _selectLicenseExpiryDate,
            child: Container(
              padding: const EdgeInsets.all(AppSpacing.inputPadding),
              decoration: BoxDecoration(
                color: AppColors.inputBackground,
                borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
                border: Border.all(color: AppColors.inputBorder),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: AppColors.onSurfaceVariant,
                    size: AppSpacing.iconMd,
                  ),
                  
                  const SizedBox(width: AppSpacing.md),
                  
                  Expanded(
                    child: Text(
                      _licenseExpiryDate != null
                          ? 'Expires: ${_licenseExpiryDate!.day}/${_licenseExpiryDate!.month}/${_licenseExpiryDate!.year}'
                          : 'Select license expiry date',
                      style: _licenseExpiryDate != null
                          ? AppTextStyles.inputText
                          : AppTextStyles.inputPlaceholder,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // License image upload
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppSpacing.lg),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
              border: Border.all(
                color: AppColors.border,
                style: BorderStyle.solid,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.camera_alt_outlined,
                  size: AppSpacing.iconXl,
                  color: AppColors.onSurfaceVariant,
                ),
                
                const SizedBox(height: AppSpacing.md),
                
                Text(
                  _licenseImagePath != null ? 'License Image Uploaded' : 'Upload License Image',
                  style: AppTextStyles.buttonMedium,
                ),
                
                const SizedBox(height: AppSpacing.sm),
                
                Text(
                  'Take a clear photo of your driver\'s license',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppSpacing.md),
                
                CustomButton(
                  text: _licenseImagePath != null ? 'Change Image' : 'Take Photo',
                  onPressed: _uploadLicenseImage,
                  buttonType: ButtonType.secondary,
                  icon: Icons.camera_alt,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Vehicle Information',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Add your vehicle details. You can add multiple vehicles.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Vehicles list
          ..._vehicles.asMap().entries.map((entry) {
            final int index = entry.key;
            final VehicleForm vehicle = entry.value;
            
            return Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.lg),
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Vehicle ${index + 1}',
                        style: AppTextStyles.buttonMedium,
                      ),
                      
                      const Spacer(),
                      
                      if (_vehicles.length > 1)
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          onPressed: () => _removeVehicle(index),
                          color: AppColors.error,
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: vehicle.makeController,
                          label: 'Make',
                          hintText: 'e.g., Toyota',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Make is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      
                      const SizedBox(width: AppSpacing.md),
                      
                      Expanded(
                        child: CustomTextField(
                          controller: vehicle.modelController,
                          label: 'Model',
                          hintText: 'e.g., Camry',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Model is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: vehicle.yearController,
                          label: 'Year',
                          hintText: '2020',
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Year is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      
                      const SizedBox(width: AppSpacing.md),
                      
                      Expanded(
                        child: CustomTextField(
                          controller: vehicle.colorController,
                          label: 'Color',
                          hintText: 'White',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Color is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: vehicle.licensePlateController,
                    label: 'License Plate',
                    hintText: 'ABC-1234',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'License plate is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  // Vehicle type dropdown
                  DropdownButtonFormField<VehicleType>(
                    value: vehicle.type,
                    decoration: const InputDecoration(
                      labelText: 'Vehicle Type',
                      border: OutlineInputBorder(),
                    ),
                    items: VehicleType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        vehicle.type = value ?? VehicleType.sedan;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Vehicle type is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: vehicle.seatingCapacityController,
                    label: 'Seating Capacity',
                    hintText: '4',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Seating capacity is required';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            );
          }),
          
          // Add vehicle button
          if (_vehicles.length < 3)
            CustomButton(
              text: 'Add Another Vehicle',
              onPressed: _addVehicle,
              buttonType: ButtonType.secondary,
              icon: Icons.add,
            ),
        ],
      ),
    );
  }

  Widget _buildPreferencesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Driver Preferences',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Set your driving preferences and what types of rides you accept.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          _buildPreferenceItem(
            title: 'Accept Shared Rides',
            subtitle: 'Allow multiple passengers in one trip',
            value: _acceptSharedRides,
            onChanged: (value) {
              setState(() {
                _acceptSharedRides = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Accept Pet-Friendly Rides',
            subtitle: 'Allow passengers to bring pets',
            value: _acceptPetFriendlyRides,
            onChanged: (value) {
              setState(() {
                _acceptPetFriendlyRides = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Accept Long Distance Rides',
            subtitle: 'Accept rides longer than 50km',
            value: _acceptLongDistanceRides,
            onChanged: (value) {
              setState(() {
                _acceptLongDistanceRides = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Accept Cash Payments',
            subtitle: 'Allow passengers to pay with cash',
            value: _acceptCashPayments,
            onChanged: (value) {
              setState(() {
                _acceptCashPayments = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingHoursStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Working Hours',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Set your preferred working hours for each day of the week.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Working hours for each day
          ...DayOfWeek.values.map((day) {
            final hours = _workingHours[day]!;
            
            return Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.md),
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          day.displayName,
                          style: AppTextStyles.buttonMedium,
                        ),
                      ),
                      
                      Switch(
                        value: hours.isActive,
                        onChanged: (value) {
                          setState(() {
                            hours.isActive = value;
                          });
                        },
                        activeColor: AppColors.primary,
                      ),
                    ],
                  ),
                  
                  if (hours.isActive) ...[
                    const SizedBox(height: AppSpacing.md),
                    
                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectTime(day, true),
                            child: Container(
                              padding: const EdgeInsets.all(AppSpacing.md),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.border),
                                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                              ),
                              child: Text(
                                hours.startTime ?? 'Start Time',
                                style: hours.startTime != null
                                    ? AppTextStyles.inputText
                                    : AppTextStyles.inputPlaceholder,
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(width: AppSpacing.md),
                        
                        const Text('to'),
                        
                        const SizedBox(width: AppSpacing.md),
                        
                        Expanded(
                          child: InkWell(
                            onTap: () => _selectTime(day, false),
                            child: Container(
                              padding: const EdgeInsets.all(AppSpacing.md),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.border),
                                borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                              ),
                              child: Text(
                                hours.endTime ?? 'End Time',
                                style: hours.endTime != null
                                    ? AppTextStyles.inputText
                                    : AppTextStyles.inputPlaceholder,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildPreferenceItem({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: SwitchListTile(
        title: Text(
          title,
          style: AppTextStyles.buttonMedium,
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: 'Previous',
                onPressed: _previousStep,
                buttonType: ButtonType.secondary,
              ),
            ),
          
          if (_currentStep > 0) const SizedBox(width: AppSpacing.md),
          
          Expanded(
            child: CustomButton(
              text: _currentStep == _totalSteps - 1 ? 'Complete Setup' : 'Next',
              onPressed: _currentStep == _totalSteps - 1 ? _completeSetup : _nextStep,
            ),
          ),
        ],
      ),
    );
  }

  void _initializeWorkingHours() {
    for (final day in DayOfWeek.values) {
      _workingHours[day] = WorkingHoursForm();
    }
  }

  void _addVehicle() {
    setState(() {
      _vehicles.add(VehicleForm());
    });
  }

  void _removeVehicle(int index) {
    setState(() {
      _vehicles[index].dispose();
      _vehicles.removeAt(index);
    });
  }

  void _selectLicenseExpiryDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );
    
    if (picked != null) {
      setState(() {
        _licenseExpiryDate = picked;
      });
    }
  }

  void _uploadLicenseImage() {
    // TODO: Implement image upload
    setState(() {
      _licenseImagePath = 'path/to/license/image.jpg';
    });
  }

  void _selectTime(DayOfWeek day, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    
    if (picked != null) {
      final String timeString = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      
      setState(() {
        if (isStartTime) {
          _workingHours[day]!.startTime = timeString;
        } else {
          _workingHours[day]!.endTime = timeString;
        }
      });
    }
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeSetup() {
    // TODO: Implement driver profile creation
    // Validate all forms
    // Create driver profile
    // Navigate to main app
  }
}

/// Vehicle form helper class
class VehicleForm {
  final TextEditingController makeController = TextEditingController();
  final TextEditingController modelController = TextEditingController();
  final TextEditingController yearController = TextEditingController();
  final TextEditingController colorController = TextEditingController();
  final TextEditingController licensePlateController = TextEditingController();
  final TextEditingController seatingCapacityController = TextEditingController();
  VehicleType type = VehicleType.sedan;

  void dispose() {
    makeController.dispose();
    modelController.dispose();
    yearController.dispose();
    colorController.dispose();
    licensePlateController.dispose();
    seatingCapacityController.dispose();
  }

  CreateVehicleRequest toRequest() {
    return CreateVehicleRequest(
      make: makeController.text.trim(),
      model: modelController.text.trim(),
      year: int.parse(yearController.text.trim()),
      color: colorController.text.trim(),
      licensePlate: licensePlateController.text.trim(),
      type: type,
      seatingCapacity: int.parse(seatingCapacityController.text.trim()),
    );
  }
}

/// Working hours form helper class
class WorkingHoursForm {
  bool isActive = false;
  String? startTime;
  String? endTime;

  void dispose() {
    // No controllers to dispose
  }

  CreateWorkingHoursRequest? toRequest(DayOfWeek day) {
    if (!isActive || startTime == null || endTime == null) {
      return null;
    }
    
    return CreateWorkingHoursRequest(
      dayOfWeek: day,
      startTime: startTime!,
      endTime: endTime!,
    );
  }
}
