import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/theme/index.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../models/rider_profile.dart';

/// Rider profile setup screen for emergency contacts and preferences
class RiderProfileSetupScreen extends ConsumerStatefulWidget {
  const RiderProfileSetupScreen({super.key});

  @override
  ConsumerState<RiderProfileSetupScreen> createState() => _RiderProfileSetupScreenState();
}

class _RiderProfileSetupScreenState extends ConsumerState<RiderProfileSetupScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 3;

  // Emergency contacts
  final List<EmergencyContactForm> _emergencyContacts = [];

  // Preferences
  bool _allowSharedRides = false;
  bool _allowPetFriendlyRides = false;
  bool _preferFemaleDrivers = false;
  bool _accessibilityNeeds = false;
  final TextEditingController _accessibilityDetailsController = TextEditingController();

  // Payment preferences
  String? _preferredPaymentMethod;
  final List<String> _paymentMethods = ['Credit Card', 'Debit Card', 'Cash', 'Mobile Payment'];

  @override
  void initState() {
    super.initState();
    // Add one emergency contact by default
    _addEmergencyContact();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _accessibilityDetailsController.dispose();
    for (final contact in _emergencyContacts) {
      contact.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Complete Your Profile'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: false, // TODO: Connect to state management
        child: Column(
          children: [
            // Progress indicator
            _buildProgressIndicator(),
            
            const SizedBox(height: AppSpacing.lg),
            
            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                },
                children: [
                  _buildEmergencyContactsStep(),
                  _buildPreferencesStep(),
                  _buildPaymentStep(),
                ],
              ),
            ),
            
            // Navigation buttons
            _buildNavigationButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.containerPadding),
      child: Column(
        children: [
          Row(
            children: List.generate(_totalSteps, (index) {
              final bool isActive = index <= _currentStep;
              final bool isCurrent = index == _currentStep;
              
              return Expanded(
                child: Container(
                  height: 4,
                  margin: EdgeInsets.only(
                    right: index < _totalSteps - 1 ? AppSpacing.sm : 0,
                  ),
                  decoration: BoxDecoration(
                    color: isActive ? AppColors.primary : AppColors.lightGray,
                    borderRadius: BorderRadius.circular(AppSpacing.radiusSm),
                  ),
                ),
              );
            }),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          Text(
            'Step ${_currentStep + 1} of $_totalSteps',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContactsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Emergency Contacts',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Add emergency contacts for your safety. We\'ll notify them in case of an emergency.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Emergency contacts list
          ..._emergencyContacts.asMap().entries.map((entry) {
            final int index = entry.key;
            final EmergencyContactForm contact = entry.value;
            
            return Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.lg),
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
                border: Border.all(color: AppColors.border),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Contact ${index + 1}',
                        style: AppTextStyles.buttonMedium,
                      ),
                      
                      const Spacer(),
                      
                      if (_emergencyContacts.length > 1)
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          onPressed: () => _removeEmergencyContact(index),
                          color: AppColors.error,
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: contact.nameController,
                    label: 'Full Name',
                    hintText: 'Enter contact name',
                    prefixIcon: Icons.person_outline,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Name is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: contact.phoneController,
                    label: 'Phone Number',
                    hintText: 'Enter phone number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: Icons.phone_outlined,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Phone number is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: contact.relationshipController,
                    label: 'Relationship',
                    hintText: 'e.g., Mother, Father, Spouse',
                    prefixIcon: Icons.family_restroom,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Relationship is required';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: AppSpacing.md),
                  
                  CustomTextField(
                    controller: contact.emailController,
                    label: 'Email (Optional)',
                    hintText: 'Enter email address',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: Icons.email_outlined,
                  ),
                ],
              ),
            );
          }),
          
          // Add contact button
          if (_emergencyContacts.length < 3)
            CustomButton(
              text: 'Add Another Contact',
              onPressed: _addEmergencyContact,
              buttonType: ButtonType.secondary,
              icon: Icons.add,
            ),
        ],
      ),
    );
  }

  Widget _buildPreferencesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ride Preferences',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Set your preferences to get the best ride experience.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Preferences
          _buildPreferenceItem(
            title: 'Allow Shared Rides',
            subtitle: 'Share rides with other passengers to save money',
            value: _allowSharedRides,
            onChanged: (value) {
              setState(() {
                _allowSharedRides = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Pet-Friendly Rides',
            subtitle: 'Request rides that allow pets',
            value: _allowPetFriendlyRides,
            onChanged: (value) {
              setState(() {
                _allowPetFriendlyRides = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Prefer Female Drivers',
            subtitle: 'Request female drivers when available',
            value: _preferFemaleDrivers,
            onChanged: (value) {
              setState(() {
                _preferFemaleDrivers = value;
              });
            },
          ),
          
          _buildPreferenceItem(
            title: 'Accessibility Needs',
            subtitle: 'I have special accessibility requirements',
            value: _accessibilityNeeds,
            onChanged: (value) {
              setState(() {
                _accessibilityNeeds = value;
              });
            },
          ),
          
          if (_accessibilityNeeds) ...[
            const SizedBox(height: AppSpacing.md),
            CustomTextField(
              controller: _accessibilityDetailsController,
              label: 'Accessibility Details',
              hintText: 'Describe your accessibility needs',
              maxLines: 3,
              validator: (value) {
                if (_accessibilityNeeds && (value == null || value.isEmpty)) {
                  return 'Please describe your accessibility needs';
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Preferences',
            style: AppTextStyles.heading2,
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          Text(
            'Choose your preferred payment method for rides.',
            style: AppTextStyles.body2.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Payment methods
          ..._paymentMethods.map((method) {
            return Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.md),
              child: RadioListTile<String>(
                title: Text(method),
                value: method,
                groupValue: _preferredPaymentMethod,
                onChanged: (value) {
                  setState(() {
                    _preferredPaymentMethod = value;
                  });
                },
                activeColor: AppColors.primary,
              ),
            );
          }),
          
          const SizedBox(height: AppSpacing.xl),
          
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.primaryWithOpacity(0.1),
              borderRadius: BorderRadius.circular(AppSpacing.radiusLg),
              border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.primary,
                  size: AppSpacing.iconMd,
                ),
                
                const SizedBox(width: AppSpacing.md),
                
                Expanded(
                  child: Text(
                    'You can change your payment method anytime in settings.',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceItem({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      child: SwitchListTile(
        title: Text(
          title,
          style: AppTextStyles.buttonMedium,
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.containerPadding),
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: CustomButton(
                text: 'Previous',
                onPressed: _previousStep,
                buttonType: ButtonType.secondary,
              ),
            ),
          
          if (_currentStep > 0) const SizedBox(width: AppSpacing.md),
          
          Expanded(
            child: CustomButton(
              text: _currentStep == _totalSteps - 1 ? 'Complete Setup' : 'Next',
              onPressed: _currentStep == _totalSteps - 1 ? _completeSetup : _nextStep,
            ),
          ),
        ],
      ),
    );
  }

  void _addEmergencyContact() {
    setState(() {
      _emergencyContacts.add(EmergencyContactForm());
    });
  }

  void _removeEmergencyContact(int index) {
    setState(() {
      _emergencyContacts[index].dispose();
      _emergencyContacts.removeAt(index);
    });
  }

  void _nextStep() {
    if (_currentStep < _totalSteps - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeSetup() {
    // TODO: Implement profile creation
    // Validate all forms
    // Create rider profile
    // Navigate to main app
  }
}

/// Emergency contact form helper class
class EmergencyContactForm {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController relationshipController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    relationshipController.dispose();
    emailController.dispose();
  }

  CreateEmergencyContactRequest toRequest() {
    return CreateEmergencyContactRequest(
      name: nameController.text.trim(),
      phoneNumber: phoneController.text.trim(),
      relationship: relationshipController.text.trim(),
      email: emailController.text.trim().isEmpty ? null : emailController.text.trim(),
    );
  }
}
